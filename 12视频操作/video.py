# import cv2
# import numpy as np
#
# # 读取图片
# img1 = cv2.imread('Master/1.png')
# img2 = cv2.imread('Master/2.png')
#
# # 确保两张图片的尺寸相同
# if img1.shape != img2.shape:
#     img2 = cv2.resize(img2, (img1.shape[1], img1.shape[0]))
#
# # 创建一个VideoWriter对象
# fourcc = cv2.VideoWriter_fourcc(*'mp4v')
# video = cv2.VideoWriter('mp4/output.mp4', fourcc, 1, (img1.shape[1], img1.shape[0]))
#
# # 将第一张图片添加到视频中，重复添加以达到2s的播放时间
# for _ in range(3):
#     video.write(img1)
#
# # 将第二张图片添加到视频中，重复添加以达到6s的播放时间
# for _ in range(7):
#     video.write(img2)
#
# # 释放VideoWriter
# video.release()

import os
import re

# 指定视频目录路径（替换为你的实际路径）
directory = "/Users/<USER>/Downloads/视频/zhibo"
os.chdir(directory)

# 定义正则表达式模式（匹配固定前缀+序号结构）
pattern = r'^来自“超哥炼AI”上传的内容 - \d{3} - '

for filename in os.listdir():
    # 跳过非视频文件（可选）
    if not filename.lower().endswith(('.mp4', '.avi', '.mov')):
        continue

    # 执行正则替换
    new_name = re.sub(pattern, '', filename)

    # 仅当名称变化时才重命名
    if new_name != filename:
        os.rename(filename, new_name)
        print(f"重命名成功: {filename} → {new_name}")