import docx
import pandas as pd

def is_word_candidate(line):
    # Return True if line contains no CJK characters (Chinese characters)
    for c in line:
        if '\u4e00' <= c <= '\u9fff':
            # Contains Chinese character
            return False
    return True

def is_irrelevant(line):
    line = line.strip()

    if line == '':
        return True

    if line.startswith('P') and line[1:].isdigit():
        return True

    if line.isdigit():
        return True

    return False

# Read the Word document
doc = docx.Document('高中必背单词大纲.docx')

# Extract text from paragraphs
lines = [para.text.strip() for para in doc.paragraphs]

# Clean up lines
lines = [line for line in lines if not is_irrelevant(line)]

# Process lines to get word-annotation pairs
index = 0
words_annotations = []

while index < len(lines):
    line = lines[index].strip()

    if is_word_candidate(line):
        word = line
        index +=1
        annotation_lines = []

        while index < len(lines) and not is_word_candidate(lines[index]):
            annotation_lines.append(lines[index].strip())
            index +=1

        annotation = ' '.join(annotation_lines).strip()
        words_annotations.append((word, annotation))
    else:
        index +=1

# Create a pandas DataFrame
df = pd.DataFrame(words_annotations, columns=['Word', 'Annotation'])

# Write DataFrame to Excel
df.to_excel('extracted_words.xlsx', index=False)

print("Data has been successfully extracted to 'extracted_words.xlsx'.")
