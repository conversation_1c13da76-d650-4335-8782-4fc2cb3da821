import numpy as np
from scipy.spatial import distance

# 定义五个城市的经纬度
coordinates = {
    "武汉": (114.3055, 30.5928),
    "杭州": (120.1551, 30.2741),
    "张家界": (110.4792, 29.1171),
    "宜昌": (111.2865, 30.6919),
    "荆州": (112.2397, 30.3352),
}

# 将经纬度转换为笛卡尔坐标系下的坐标
def geodetic_to_cartesian(lon, lat, R=6371):
    """
    Convert geodetic coordinates to Cartesian coordinates.
    :param lon: Longitude in degrees
    :param lat: Latitude in degrees
    :param R: Earth's radius in kilometers (default: 6371 km)
    :return: (x, y, z) in kilometers
    """
    lon_rad = np.radians(lon)
    lat_rad = np.radians(lat)
    x = R * np.cos(lat_rad) * np.cos(lon_rad)
    y = R * np.cos(lat_rad) * np.sin(lon_rad)
    z = R * np.sin(lat_rad)
    return x, y, z

# 将所有城市的经纬度转换为笛卡尔坐标
cities_cartesian = {city: geodetic_to_cartesian(*coords) for city, coords in coordinates.items()}

# 计算中心点坐标（算术平均）
avg_x = np.mean([coord[0] for coord in cities_cartesian.values()])
avg_y = np.mean([coord[1] for coord in cities_cartesian.values()])
avg_z = np.mean([coord[2] for coord in cities_cartesian.values()])
center_cartesian = (avg_x, avg_y, avg_z)

# 将中心点坐标转换回地理坐标
def cartesian_to_geodetic(x, y, z, R=6371):
    """
    Convert Cartesian coordinates to geodetic coordinates.
    :param x: Cartesian x coordinate
    :param y: Cartesian y coordinate
    :param z: Cartesian z coordinate
    :param R: Earth's radius in kilometers (default: 6371 km)
    :return: (longitude, latitude) in degrees
    """
    lat_rad = np.arcsin(z / R)
    lon_rad = np.arctan2(y, x)
    lon = np.degrees(lon_rad)
    lat = np.degrees(lat_rad)
    return lon, lat

# 获取中心点的经纬度
center_geodetic = cartesian_to_geodetic(*center_cartesian)
center_geodetic
