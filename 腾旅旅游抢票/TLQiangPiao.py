import numpy as np

# 地球半径（单位：公里）
EARTH_RADIUS = 6371

# 定义城市的经纬度
coordinates = {
    "武汉": (114.3055, 30.5928),
    "杭州": (120.1551, 30.2741),
    "张家界": (110.4792, 29.1171),
    "宜昌": (111.2865, 30.6919),
    "荆州": (112.2397, 30.3352),
}

# 将经纬度转换为笛卡尔坐标
def geodetic_to_cartesian(lon, lat):
    """
    Convert geodetic coordinates to Cartesian coordinates.
    """
    lon_rad = np.radians(lon)
    lat_rad = np.radians(lat)
    x = EARTH_RADIUS * np.cos(lat_rad) * np.cos(lon_rad)
    y = EARTH_RADIUS * np.cos(lat_rad) * np.sin(lon_rad)
    z = EARTH_RADIUS * np.sin(lat_rad)
    return x, y, z

# 转换所有城市坐标
cities_cartesian = {city: geodetic_to_cartesian(lon, lat) for city, (lon, lat) in coordinates.items()}

# 计算坐标的平均值
avg_x = np.mean([coord[0] for coord in cities_cartesian.values()])
avg_y = np.mean([coord[1] for coord in cities_cartesian.values()])
avg_z = np.mean([coord[2] for coord in cities_cartesian.values()])

# 将笛卡尔坐标转换回地理坐标
def cartesian_to_geodetic(x, y, z):
    """
    Convert Cartesian coordinates back to geodetic coordinates.
    """
    lat_rad = np.arcsin(z / EARTH_RADIUS)
    lon_rad = np.arctan2(y, x)
    lon = np.degrees(lon_rad)
    lat = np.degrees(lat_rad)
    return lon, lat

# 计算并打印中心点的地理坐标
center_lon, center_lat = cartesian_to_geodetic(avg_x, avg_y, avg_z)
print(f"Calculated geographic center is at Longitude: {center_lon}, Latitude: {center_lat}")
