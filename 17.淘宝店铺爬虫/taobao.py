import requests
from bs4 import BeautifulSoup

url = 'https://shop260108725.taobao.com/shop/view_shop.htm'  # 这里填写你要爬取的淘宝店铺的URL

response = requests.get(url)
soup = BeautifulSoup(response.text, 'html.parser')

items = soup.find_all('div', class_='item')  # 这里的class可能需要你自己去淘宝店铺的网页源代码中查找

for item in items:
    title = item.find('div', class_='title').text  # 同样，这里的class也需要你自己去查找
    price = item.find('div', class_='price').text
    print(f'宝贝名称：{title}，价格：{price}')
