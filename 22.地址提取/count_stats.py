#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统计行政区划数据
"""

import json

def count_administrative_divisions():
    with open('administrative_divisions.json', 'r', encoding='utf-8') as f:
        data = json.load(f)

    total_count = 0
    province_count = len(data)
    city_count = 0
    district_count = 0
    provincial_direct_count = 0

    for province in data:
        total_count += 1
        if 'children' in province:
            for child in province['children']:
                total_count += 1
                if child['code'][2:4] == '90':
                    provincial_direct_count += 1
                elif 'children' in child:
                    city_count += 1
                    for district in child['children']:
                        total_count += 1
                        district_count += 1
                else:
                    district_count += 1

    print(f'📊 行政区划统计结果:')
    print(f'总计: {total_count} 个行政区')
    print(f'省级: {province_count} 个')
    print(f'市级: {city_count} 个')
    print(f'区县级: {district_count} 个')
    print(f'省直辖县级: {provincial_direct_count} 个')
    
    # 显示一些省直辖县级行政单位的例子
    print(f'\n🏛️ 省直辖县级行政单位示例:')
    count = 0
    for province in data:
        if 'children' in province:
            for child in province['children']:
                if child['code'][2:4] == '90' and count < 5:
                    print(f'  - {child["value"]} ({child["code"]}) - {province["value"]}')
                    count += 1

if __name__ == "__main__":
    count_administrative_divisions() 