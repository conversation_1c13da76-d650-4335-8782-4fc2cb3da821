# 行政区划数据提取工具

## 📖 项目介绍

这是一个Python脚本，用于将行政区划代码MD文件转换为JSON格式。特别适合Python初学者学习文件处理、数据结构和JSON操作。

## 📁 文件说明

- `行政区划代码 单位名称.md` - 原始数据文件
- `extract_regions.py` - Python提取脚本（带详细注释）
- `administrative_divisions.json` - 生成的JSON文件
- `count_stats.py` - 数据统计脚本
- `README.md` - 使用说明（本文件）

## 🚀 如何使用

### 1. 环境要求
- Python 3.6 或更高版本
- 无需安装额外的第三方库（使用Python标准库）

### 2. 运行步骤

**方式1：从项目目录内运行（推荐）**
```bash
# 进入项目目录
cd "22.地址提取"

# 运行脚本
python3 extract_regions.py
```

**方式2：从上级目录运行**
```bash
# 在Python目录下直接运行
python3 "22.地址提取/extract_regions.py"
```

**方式3：使用python命令（如果已配置）**
```bash
# 替代python3命令
python extract_regions.py
```

### 3. 智能文件查找
脚本现在支持智能文件查找，会自动在以下位置寻找MD文件：
1. 当前目录
2. `22.地址提取/` 目录（从上级目录运行时）
3. 脚本所在目录

### 4. 查看结果
- 脚本运行完成后，会在MD文件同目录下生成 `administrative_divisions.json` 文件
- 终端会显示处理进度和结果统计

### 5. 统计验证（可选）
```bash
# 运行统计脚本验证数据完整性
python3 count_stats.py
```

## 📊 输出格式

生成的JSON文件格式如下：

```json
[
  {
    "code": "110000",
    "value": "北京市",
    "children": [
      {
        "code": "110101",
        "value": "东城区"
      },
      {
        "code": "110102",
        "value": "西城区"
      }
    ]
  }
]
```

## 🏛️ 特殊处理：省直辖县级行政单位

本脚本正确处理了省直辖县级行政单位，这些行政区的特点是：
- 代码第三、四位为90（如419001、429004等）
- 直接隶属于省级行政区，不属于任何地级市
- 在JSON中直接作为省级行政区的children

**示例**：
- 河南省 → 济源市 (419001)
- 湖北省 → 仙桃市 (429004)、潜江市 (429005)、天门市 (429006)、神农架林区 (429021)
- 海南省 → 五指山市 (469001)、琼海市 (469002) 等
- 新疆维吾尔自治区 → 石河子市 (659001)、阿拉尔市 (659002) 等

## 📈 数据统计

当前版本处理结果：
- **总计**: 3,209 个行政区
- **省级**: 34 个
- **市级**: 328 个
- **区县级**: 2,815 个
- **省直辖县级**: 32 个

## 🎓 学习要点

### 对于Python初学者，这个项目可以学到：

1. **文件操作**
   - 如何读取文本文件
   - 文件编码处理（UTF-8）
   - 使用 `with` 语句安全操作文件

2. **数据结构**
   - 列表（list）的使用
   - 字典（dict）的创建和操作
   - 嵌套数据结构的设计

3. **字符串处理**
   - 字符串方法：`strip()`, `startswith()`, `endswith()`
   - 字符串替换和清理
   - 字符串切片：`code[2:4]` 获取特定位置的字符

4. **正则表达式**
   - 基本的正则表达式语法
   - 使用 `re.match()` 匹配模式
   - 分组捕获数据
   - 使用 `^\d{6}\s` 匹配以6位数字开头的行

5. **控制流程**
   - 条件判断（if/elif/else）
   - 循环遍历（for）
   - 流程控制（continue）

6. **异常处理**
   - try/except 语句
   - 不同类型异常的处理

7. **JSON处理**
   - 将Python对象转换为JSON
   - JSON格式化输出

8. **文件路径处理**
   - `os.path.exists()` 检查文件是否存在
   - `os.path.dirname()` 获取文件所在目录
   - `os.path.join()` 拼接文件路径
   - `__file__` 获取当前脚本文件路径

9. **行政区划代码规则**
   - XX0000：省级行政区
   - XX0000：市级行政区（后2位为00但不是省级）
   - XX90XX：省直辖县级行政单位（第3-4位为90）
   - XXXXXX：普通区县级行政区

10. **代码优化原则**
    - 使用正则表达式替代硬编码判断
    - 提高代码的通用性和可维护性
    - 减少特殊情况的枚举
    - 智能文件路径处理

## 🔧 代码结构

```
extract_regions.py
├── find_md_file()                   # 智能查找MD文件
├── parse_administrative_divisions()  # 解析MD文件
├── clean_data()                     # 清理数据结构
└── main()                          # 主函数
```

### 函数说明：

- **`find_md_file()`**: 智能查找MD文件位置，支持从不同目录运行脚本
- **`parse_administrative_divisions()`**: 读取MD文件，解析每一行数据，根据代码规则判断行政级别
- **`clean_data()`**: 优化数据结构，移除不必要的空字段
- **`main()`**: 程序入口，协调各个函数的执行

## 🐛 常见问题

### Q: 运行时提示"找不到文件"
A: 新版本脚本支持智能文件查找，如果仍然找不到文件，请检查：
- MD文件是否存在
- 文件名是否正确：`行政区划代码 单位名称.md`
- 是否在正确的目录结构中

### Q: 中文显示乱码
A: 确保使用UTF-8编码，现代Python版本通常默认支持。

### Q: 想修改输出格式
A: 可以修改 `clean_data()` 函数中的数据结构定义。

### Q: 省直辖县级行政单位没有被正确处理
A: 最新版本已修复此问题，确保使用最新的脚本版本。

### Q: 从不同目录运行脚本
A: v1.3版本支持从任何目录运行，脚本会自动查找MD文件位置。

## 🔄 版本更新

### v1.3 (最新)
- ✅ 新增智能文件查找功能
- ✅ 支持从任何目录运行脚本
- ✅ 自动确定输出文件位置
- ✅ 改进错误提示信息

### v1.2
- ✅ 优化过滤逻辑：使用正则表达式 `^\d{6}\s` 替代硬编码判断
- ✅ 提高代码通用性和可维护性
- ✅ 减少特殊情况的枚举，更加优雅的解决方案

### v1.1
- ✅ 修复省直辖县级行政单位处理bug
- ✅ 正确处理代码第三四位为90的行政区
- ✅ 添加数据统计功能
- ✅ 改进错误处理和用户提示

### v1.0
- ✅ 基本的行政区划数据提取功能
- ✅ 支持省、市、区县三级结构

## 💡 设计思路

### 智能文件查找
新版本添加了智能文件查找功能，支持多种运行方式：

```python
def find_md_file():
    possible_paths = [
        "行政区划代码 单位名称.md",  # 当前目录
        "22.地址提取/行政区划代码 单位名称.md",  # 上级目录运行
        os.path.join(os.path.dirname(__file__), "行政区划代码 单位名称.md")  # 脚本所在目录
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            return path
    return None
```

**优势**：
- 用户友好：无需关心运行目录
- 灵活性高：支持多种使用场景
- 错误提示清晰：明确指出可能的文件位置

### 数据过滤策略
使用正则表达式判断有效数据行：
```python
if not re.match(r'^\d{6}\s', line):
    continue
```

**优势**：
- 更通用：自动过滤所有非标准格式的行
- 更简洁：一行代码替代多个条件判断
- 更可靠：基于数据格式而非具体内容
- 更易维护：数据格式变化时无需修改代码

## 📚 扩展学习

完成这个项目后，你可以尝试：

1. **添加新功能**
   - 支持不同的输入格式
   - 添加数据验证
   - 生成统计报告

2. **优化代码**
   - 使用面向对象编程
   - 添加配置文件支持
   - 提高处理大文件的性能

3. **学习相关技术**
   - 数据库操作
   - Web API开发
   - 数据可视化

## 📞 技术支持

如果在学习过程中遇到问题，建议：

1. 仔细阅读代码注释
2. 使用Python调试器逐步执行
3. 查阅Python官方文档
4. 在编程社区寻求帮助

---

**祝你学习愉快！🎉** 