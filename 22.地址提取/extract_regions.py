#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
行政区划数据提取脚本
功能：从MD文件中提取行政区划代码和名称，转换为JSON格式
作者：AI助手
适用：Python初学者学习
"""

# 导入需要的模块
import json  # 用于处理JSON数据
import re    # 用于正则表达式匹配
import os    # 用于文件路径操作

def find_md_file():
    """
    自动查找MD文件的位置
    支持从不同目录运行脚本
    """
    # 可能的文件路径
    possible_paths = [
        "行政区划代码 单位名称.md",  # 当前目录
        "22.地址提取/行政区划代码 单位名称.md",  # 上级目录运行
        os.path.join(os.path.dirname(__file__), "行政区划代码 单位名称.md")  # 脚本所在目录
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            return path
    
    return None

def parse_administrative_divisions(file_path):
    """
    解析行政区划数据的主要函数
    
    参数:
        file_path (str): MD文件的路径
    
    返回:
        list: 包含所有行政区划数据的列表
    """
    # 创建一个空列表来存储最终结果
    result = []
    
    # 用于跟踪当前正在处理的省份和城市
    current_province = None  # 当前省份
    current_city = None      # 当前城市
    
    # 打开并读取MD文件
    # 'r' 表示只读模式，'utf-8' 指定编码格式
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()  # 读取所有行到列表中
    
    # 逐行处理文件内容
    for line in lines:
        line = line.strip()  # 去除行首行尾的空白字符
        
        # 使用正则表达式检查行是否以6位数字开头
        # 这种方法比硬编码特定文本更通用和可靠
        if not re.match(r'^\d{6}\s', line):
            continue  # 如果不是以6位数字开头，跳过这一行
            
        # 使用正则表达式匹配行政区划代码和名称
        # r'^(\d{6})\s+(.+)$' 的含义：
        # ^ : 行的开始
        # (\d{6}) : 匹配6位数字（行政区划代码）
        # \s+ : 匹配一个或多个空白字符
        # (.+) : 匹配一个或多个任意字符（地名）
        # $ : 行的结束
        match = re.match(r'^(\d{6})\s+(.+)$', line)
        if not match:
            continue  # 如果不匹配完整格式，跳过这一行
            
        # 提取匹配到的代码和名称
        code = match.group(1)  # 第一个括号内的内容（代码）
        name = match.group(2)  # 第二个括号内的内容（名称）
        
        # 清理名称中的特殊标记
        if '*' in name:
            name = name.replace('*', '')  # 移除星号标记
        
        # 根据代码判断行政级别
        # 中国行政区划代码规则：
        # XXXXXX - 6位数字
        # XX0000 - 省级（后4位为0）
        # XX0000 - 市级（后2位为0，但不是省级）
        # XX90XX - 省直辖县级行政单位（第三四位为90）
        # XXXXXX - 区县级（其他情况）
        
        if code.endswith('0000'):  # 省级行政区
            # 创建新的省份数据结构
            current_province = {
                "code": code,      # 行政区划代码
                "value": name,     # 行政区名称
                "children": []     # 子级行政区列表
            }
            result.append(current_province)  # 添加到结果列表
            current_city = None  # 重置当前城市
            
        elif code.endswith('00') and not code.endswith('0000'):  # 市级行政区
            # 确保当前有省份在处理
            if current_province is not None:
                # 创建新的城市数据结构
                current_city = {
                    "code": code,
                    "value": name,
                    "children": []
                }
                # 将城市添加到当前省份的子级列表中
                current_province["children"].append(current_city)
            
        elif code[2:4] == '90':  # 省直辖县级行政单位（第三四位为90）
            # 这些是省直辖县级行政单位，直接添加到当前省份下
            if current_province is not None:
                district = {
                    "code": code,
                    "value": name
                }
                current_province["children"].append(district)
                
        else:  # 区县级行政区
            # 创建区县数据结构（没有children，因为是最底层）
            district = {
                "code": code,
                "value": name
            }
            
            if current_city is not None:
                # 如果当前有城市，将区县添加到城市下
                current_city["children"].append(district)
            elif current_province is not None:
                # 如果没有城市但有省份，直接添加到省份下
                # 这种情况适用于直辖市
                current_province["children"].append(district)
    
    return result  # 返回处理完的数据

def clean_data(data):
    """
    清理数据，优化数据结构
    
    参数:
        data (list): 原始数据列表
    
    返回:
        list: 清理后的数据列表
    """
    cleaned_data = []  # 存储清理后的数据
    
    # 遍历每个省份
    for province in data:
        # 创建清理后的省份数据
        cleaned_province = {
            "code": province["code"],
            "value": province["value"]
        }
        
        # 如果省份有子级行政区
        if province["children"]:
            cleaned_children = []  # 存储清理后的子级数据
            
            # 遍历省份的每个子级（可能是市或区县）
            for child in province["children"]:
                # 检查这个子级是否还有自己的子级
                if "children" in child and child["children"]:
                    # 这是一个市，有下级区县
                    cleaned_child = {
                        "code": child["code"],
                        "value": child["value"],
                        "children": child["children"]  # 保留区县列表
                    }
                    cleaned_children.append(cleaned_child)
                else:
                    # 这是一个区县，或者是没有下级的市
                    cleaned_child = {
                        "code": child["code"],
                        "value": child["value"]
                        # 注意：这里不添加children字段
                    }
                    cleaned_children.append(cleaned_child)
            
            # 将清理后的子级列表添加到省份中
            cleaned_province["children"] = cleaned_children
        
        # 将清理后的省份添加到结果中
        cleaned_data.append(cleaned_province)
    
    return cleaned_data

def main():
    """
    主函数：程序的入口点
    """
    print("🚀 开始处理行政区划数据...")
    
    # 自动查找MD文件
    input_file = find_md_file()
    if input_file is None:
        print("❌ 错误：找不到 '行政区划代码 单位名称.md' 文件")
        print("请确保文件存在于以下位置之一：")
        print("  1. 当前目录")
        print("  2. 22.地址提取/ 目录")
        print("  3. 脚本所在目录")
        return
    
    # 确定输出文件路径（与输入文件在同一目录）
    output_dir = os.path.dirname(input_file) if os.path.dirname(input_file) else "."
    output_file = os.path.join(output_dir, "administrative_divisions.json")
    
    print(f"📖 正在读取文件: {input_file}")
    print(f"📁 输出文件将保存到: {output_file}")
    
    try:
        # 第一步：解析MD文件中的数据
        print("📊 正在解析行政区划数据...")
        data = parse_administrative_divisions(input_file)
        
        # 第二步：清理和优化数据结构
        print("🧹 正在清理数据...")
        cleaned_data = clean_data(data)
        
        # 第三步：保存为JSON文件
        print("💾 正在保存JSON文件...")
        with open(output_file, 'w', encoding='utf-8') as f:
            # json.dump参数说明：
            # ensure_ascii=False : 允许中文字符
            # indent=2 : 格式化输出，缩进2个空格
            json.dump(cleaned_data, f, ensure_ascii=False, indent=2)
        
        # 显示处理结果
        print("✅ 数据提取完成！")
        print(f"📈 共提取了 {len(cleaned_data)} 个省级行政区")
        print(f"📁 结果已保存到: {output_file}")
        
        # 统计省直辖县级行政单位数量
        provincial_direct_count = 0
        for province in cleaned_data:
            if 'children' in province:
                for child in province['children']:
                    # 检查是否是省直辖县级行政单位（代码第三四位为90）
                    if child['code'][2:4] == '90':
                        provincial_direct_count += 1
        
        print(f"🏛️ 其中包含 {provincial_direct_count} 个省直辖县级行政单位")
        
        # 显示前几条数据作为示例
        print("\n📋 前3条数据示例:")
        for i, province in enumerate(cleaned_data[:3]):  # 只显示前3个
            print(f"{i+1}. {province['value']} ({province['code']})")
            
            # 如果省份有子级，显示前几个
            if 'children' in province:
                for j, child in enumerate(province['children'][:3]):  # 只显示前3个
                    # 标记省直辖县级行政单位
                    marker = " [省直辖]" if child['code'][2:4] == '90' else ""
                    print(f"   - {child['value']} ({child['code']}){marker}")
                    # 如果还有更多，显示省略号
                    if j >= 2 and len(province['children']) > 3:
                        remaining = len(province['children']) - 3
                        print(f"   - ... 还有 {remaining} 个")
                        break
        
        print("\n🎉 处理完成！您可以查看生成的JSON文件了。")
        
    except FileNotFoundError:
        # 文件不存在的错误处理
        print(f"❌ 错误：找不到文件 {input_file}")
        print("请确保MD文件在指定位置")
        
    except Exception as e:
        # 其他错误的处理
        print(f"❌ 处理过程中出现错误: {str(e)}")

# Python程序入口点
# 当直接运行这个脚本时，会执行main()函数
# 当作为模块导入时，不会执行main()函数
if __name__ == "__main__":
    main()

"""
学习要点：

1. 文件操作：
   - open() 函数用于打开文件
   - with 语句确保文件正确关闭
   - encoding='utf-8' 处理中文字符

2. 数据结构：
   - 列表(list)：存储多个元素
   - 字典(dict)：存储键值对
   - 嵌套结构：字典中包含列表，列表中包含字典

3. 字符串处理：
   - strip() 去除空白字符
   - startswith() 检查字符串开头
   - endswith() 检查字符串结尾
   - replace() 替换字符
   - 字符串切片：code[2:4] 获取第3-4位字符

4. 正则表达式：
   - re.match() 从字符串开头匹配
   - 括号()用于分组捕获
   - \d 匹配数字，\s 匹配空白字符
   - ^\d{6}\s 匹配以6位数字开头后跟空白字符的行

5. 控制流：
   - if/elif/else 条件判断
   - for 循环遍历
   - continue 跳过当前循环

6. 异常处理：
   - try/except 捕获和处理错误
   - 不同类型的异常分别处理

7. JSON处理：
   - json.dump() 将Python对象保存为JSON文件
   - 参数控制输出格式

8. 行政区划代码规则：
   - XX0000：省级行政区
   - XX0000：市级行政区（后2位为00但不是省级）
   - XX90XX：省直辖县级行政单位（第3-4位为90）
   - XXXXXX：普通区县级行政区

9. 代码优化原则：
   - 使用正则表达式替代硬编码判断
   - 提高代码的通用性和可维护性
   - 减少特殊情况的枚举

10. 文件路径处理：
    - os.path.exists() 检查文件是否存在
    - os.path.dirname() 获取文件所在目录
    - os.path.join() 拼接文件路径
    - __file__ 获取当前脚本文件路径
""" 