#新词发现
from smoothnlp.algorithm.phrase import extract_phrase
import re


class_name = '自媒体'
class_name_low = class_name.lower()
top_k = 100

with open('%s.txt' % class_name,'r',encoding='utf-8') as file:
	data_str = file.read()
	keyword_list = data_str.split('\n')

with open('dont.txt', 'r', encoding='utf-8') as file:
	dont_set = set(file.read().split('\n'))


word_count_dict = dict()
new_word_list = extract_phrase(keyword_list, top_k = top_k)
print(new_word_list)

for new_word in new_word_list:
	if new_word in dont_set:
		continue

	new_word_low = new_word.lower()

	if class_name_low in new_word_low or class_name_low == new_word_low: #如果主词在新词里面或者新词就是主词，没必要再做组合，直接存储
		word_count_dict[new_word] = len(re.findall(new_word_low, data_str))
		continue

	#自媒体 变现  —— 自媒体变现 还是 变现自媒体？
	add_l = re.findall('%s%s' % (class_name_low, new_word_low), data_str)
	add_r = re.findall('%s%s' % (new_word_low, class_name_low), data_str)
	if len(add_l) >= len(add_r):
		word_count_dict['%s%s' % (class_name, new_word)] = len(add_l)
	else:
		word_count_dict['%s%s' % (new_word, class_name)] = len(add_r)

for word, count in word_count_dict.items():
	print('%s\t%s' % (word, count))



