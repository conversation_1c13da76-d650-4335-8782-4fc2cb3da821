# 使用smoothnlp的新词发现功能  https://github.com/smoothnlp/SmoothNLP/tree/master/tutorials/%E6%96%B0%E8%AF%8D%E5%8F%91%E7%8E%B0
from smoothnlp.algorithm.phrase import extract_phrase



with open('自媒体.txt', 'r', encoding='utf-8') as file:
	keyword_list = file.read().split('\n')

# print(keyword_list)
new_phrases = extract_phrase(keyword_list, top_k=100)
print(new_phrases)

# import re

# s = '自媒体媒体广告'
# print(re.findall('媒体', s))
# print(len(re.findall('1', s)))