# 医院挂号信息获取程序

基于Charles抓包的HAR文件分析编写的医院挂号信息获取和监控程序。

## 功能特性

- 🏥 获取医生挂号信息
- 📅 查看可用时段和费用
- 🔍 监控多个医生的挂号情况
- ⏰ 定时自动监控（每分钟检查）
- 💾 保存可用时段信息
- 📱 支持微信小程序API
- 🚨 **多种通知方式**：
  - 📧 邮件通知
  - 📱 微信通知（Server酱）
  - 💼 企业微信通知
  - 🔊 声音提醒
  - 🖥️ 桌面通知
  - 📝 日志文件
- 🎯 **智能监控**：专门监控26.5元未约满号源

## 文件说明

- `hospital_booking.py` - **主程序**，支持定时监控和多种通知方式
- `advanced_booking.py` - 高级版本，支持多医生监控和定时查询
- `config.py` - 配置文件，包含API参数和请求头
- `notification_config.py` - 通知配置文件模板
- `setup_notifications.py` - **通知设置向导**，快速配置通知方式
- `requirements.txt` - Python依赖包列表
- `Untitled.har` - Charles抓包文件（原始数据）

## 安装依赖

```bash
pip install -r requirements.txt
```

## 快速开始

### 第一步：设置通知方式（推荐）

```bash
python setup_notifications.py
```

这个向导会帮您配置：
- 📱 **Server酱微信通知**（推荐，最简单）
- 📧 **邮件通知**（备用方案）

### 第二步：开始监控

```bash
python hospital_booking.py
```

选择「定时监控26.5元号源」，程序会：
- ⏰ 每分钟自动检查一次
- 🎯 专门监控26.5元且未约满的号源
- 🚨 发现可用号源时立即通知您
- 💾 自动保存详细信息到文件

## 使用方法详解

### 主程序功能

运行 `python hospital_booking.py` 后有三个选项：

1. **单次查询医生信息** - 查看医生的所有排班信息
2. **定时监控26.5元号源**（推荐）- 专门监控目标价格的可用号源
3. **自定义价格监控** - 监控其他价格的号源

### 高级版本使用

```bash
python advanced_booking.py
```

支持监控多个医生，适合有多个目标医生的情况。

## 配置说明

### 修改监控的医生

在 `advanced_booking.py` 的 `main()` 函数中修改：

```python
# 要监控的医生列表
doctors_to_monitor = ["100403", "其他医生代码"]
```

### 修改请求参数

在 `config.py` 中修改：

```python
# 默认请求参数
DEFAULT_PARAMS = {
    "doctor_code": "100403",  # 医生代码
    "yq_code1": "",          # 院区代码1
    "ks_code1": "",          # 科室代码1
    "schedule_type": "",     # 排班类型
    "later_than_17": True    # 是否17点后
}
```

### 修改认证信息

如果token过期或需要更换用户，在 `config.py` 中修改 `HEADERS` 中的相关字段：

```python
HEADERS = {
    "uuid": "你的uuid",
    "ukey": "你的ukey", 
    "uname": "你的用户名",
    "token": "你的token",
    # ... 其他字段
}
```

## 输出说明

### 控制台输出

程序会在控制台显示：
- 医生基本信息（姓名、职称、科室等）
- 排班信息（日期、时间、费用、状态）
- 可用时段提醒

### 文件输出

- `doctor_info_raw.json` - 原始API响应数据
- `available_slots_医生代码_时间戳.json` - 发现可用时段时的详细信息

## 注意事项

⚠️ **重要提醒**

1. **认证信息时效性**：HAR文件中的token、uuid等认证信息有时效性，过期后需要重新抓包获取
2. **请求频率**：不要设置过高的监控频率，避免对服务器造成压力
3. **网络环境**：确保网络连接稳定，程序需要访问 `tjhapp.com.cn:8013`
4. **合规使用**：请遵守医院的相关规定，合理使用挂号系统

## 故障排除

### 常见问题

1. **请求失败，状态码: 401/403**
   - 认证信息过期，需要重新抓包获取token等信息

2. **网络请求异常**
   - 检查网络连接
   - 确认医院服务器是否正常

3. **解码响应失败**
   - API响应格式可能发生变化
   - 检查返回的数据格式

### 获取新的认证信息

1. 使用Charles Proxy或其他抓包工具
2. 在手机上打开医院小程序/网页
3. 执行挂号查询操作
4. 找到对应的API请求
5. 复制新的认证信息到 `config.py`

## 扩展功能

可以根据需要添加以下功能：
- 微信/邮件通知
- 数据库存储
- Web界面
- 更多医院支持
- 自动挂号功能

## 免责声明

本程序仅用于学习和研究目的，使用者需要遵守相关法律法规和医院规定。作者不承担因使用本程序而产生的任何责任。
