#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通知设置向导
帮助用户快速配置各种通知方式
"""

import json
import os


def setup_server_chan():
    """设置Server酱微信通知"""
    print("\n=== Server酱微信通知设置 ===")
    print("Server酱是最简单的微信通知方式，推荐使用！")
    print("\n设置步骤：")
    print("1. 访问 https://sct.ftqq.com/")
    print("2. 使用微信扫码登录")
    print("3. 点击「发送消息」获取SendKey")
    print("4. 将SendKey复制到下面")
    
    send_key = input("\n请输入您的SendKey (留空跳过): ").strip()
    
    if send_key:
        # 测试发送
        import requests
        try:
            url = f"https://sctapi.ftqq.com/{send_key}.send"
            data = {
                "title": "医院挂号监控测试",
                "desp": "如果您收到这条消息，说明Server酱配置成功！"
            }
            response = requests.post(url, data=data, timeout=10)
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    print("✅ Server酱配置成功！请检查微信是否收到测试消息。")
                    return send_key
                else:
                    print(f"❌ Server酱配置失败: {result.get('message', '未知错误')}")
            else:
                print("❌ Server酱配置失败，请检查SendKey是否正确")
        except Exception as e:
            print(f"❌ 测试发送失败: {e}")
    
    return None


def setup_email():
    """设置邮件通知"""
    print("\n=== 邮件通知设置 ===")
    print("邮件通知适合在没有微信的情况下使用")
    
    sender_email = input("请输入发送者邮箱 (留空跳过): ").strip()
    if not sender_email:
        return None
    
    print(f"\n检测到邮箱类型: {sender_email.split('@')[1] if '@' in sender_email else '未知'}")
    
    # 根据邮箱类型提供SMTP配置建议
    domain = sender_email.split('@')[1] if '@' in sender_email else ''
    smtp_configs = {
        'qq.com': ('smtp.qq.com', 587),
        '163.com': ('smtp.163.com', 25),
        'gmail.com': ('smtp.gmail.com', 587),
        'outlook.com': ('smtp-mail.outlook.com', 587),
        'hotmail.com': ('smtp-mail.outlook.com', 587),
    }
    
    if domain in smtp_configs:
        smtp_server, smtp_port = smtp_configs[domain]
        print(f"建议SMTP配置: {smtp_server}:{smtp_port}")
    else:
        smtp_server = input("请输入SMTP服务器地址: ").strip()
        smtp_port = int(input("请输入SMTP端口 (默认587): ").strip() or "587")
    
    sender_password = input("请输入邮箱授权码 (不是登录密码): ").strip()
    receiver_email = input("请输入接收者邮箱: ").strip()
    
    if sender_email and sender_password and receiver_email:
        # 测试发送
        try:
            import smtplib
            from email.mime.text import MimeText
            from email.mime.multipart import MimeMultipart
            
            msg = MimeMultipart()
            msg['From'] = sender_email
            msg['To'] = receiver_email
            msg['Subject'] = "医院挂号监控测试"
            
            content = "如果您收到这封邮件，说明邮件通知配置成功！"
            msg.attach(MimeText(content, 'plain', 'utf-8'))
            
            server = smtplib.SMTP(smtp_server, smtp_port)
            server.starttls()
            server.login(sender_email, sender_password)
            server.send_message(msg)
            server.quit()
            
            print("✅ 邮件配置成功！请检查邮箱是否收到测试邮件。")
            return {
                'smtp_server': smtp_server,
                'smtp_port': smtp_port,
                'sender_email': sender_email,
                'sender_password': sender_password,
                'receiver_email': receiver_email
            }
        except Exception as e:
            print(f"❌ 邮件配置失败: {e}")
    
    return None


def generate_config_file(server_chan_key, email_config):
    """生成配置文件"""
    config_content = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动生成的通知配置文件
"""

# 邮件通知配置
EMAIL_CONFIG = {{
    "enabled": {bool(email_config)},
    "smtp_server": "{email_config.get('smtp_server', '') if email_config else ''}",
    "smtp_port": {email_config.get('smtp_port', 587) if email_config else 587},
    "sender_email": "{email_config.get('sender_email', '') if email_config else ''}",
    "sender_password": "{email_config.get('sender_password', '') if email_config else ''}",
    "receiver_email": "{email_config.get('receiver_email', '') if email_config else ''}",
}}

# Server酱微信通知配置
SERVER_CHAN_CONFIG = {{
    "enabled": {bool(server_chan_key)},
    "send_key": "{server_chan_key or ''}",
}}

# 企业微信机器人配置
WECHAT_WORK_CONFIG = {{
    "enabled": False,
    "webhook_url": "",
}}

# 本地通知配置
LOCAL_NOTIFICATION_CONFIG = {{
    "sound_alert": True,
    "desktop_notification": True,
    "save_log": True,
}}
'''
    
    with open('医院抢号系统/auto_notification_config.py', 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print(f"\n✅ 配置文件已生成: auto_notification_config.py")


def main():
    """主函数"""
    print("医院挂号监控 - 通知设置向导")
    print("="*50)
    print("本向导将帮助您设置通知方式，确保在发现可用号源时能及时收到提醒。")
    
    # 设置Server酱
    server_chan_key = setup_server_chan()
    
    # 设置邮件
    email_config = setup_email()
    
    # 生成配置文件
    if server_chan_key or email_config:
        generate_config_file(server_chan_key, email_config)
        
        print("\n" + "="*50)
        print("🎉 通知设置完成！")
        print("\n配置摘要:")
        if server_chan_key:
            print("✅ Server酱微信通知: 已配置")
        if email_config:
            print("✅ 邮件通知: 已配置")
        
        print("\n接下来您可以:")
        print("1. 运行 python hospital_booking.py")
        print("2. 选择「定时监控26.5元号源」")
        print("3. 程序会在发现可用号源时自动通知您")
        
        print("\n⚠️ 重要提醒:")
        print("- 建议在电脑不关机的情况下运行监控")
        print("- 监控间隔建议设置为1-5分钟")
        print("- 确保网络连接稳定")
        
    else:
        print("\n😔 未配置任何通知方式")
        print("您仍然可以使用程序，但不会收到自动通知")
        print("程序会在控制台显示监控结果，并保存到日志文件")


if __name__ == "__main__":
    main()
