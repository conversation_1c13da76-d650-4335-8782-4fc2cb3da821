#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通知配置文件
请根据需要填写相应的配置信息
"""

# =============================================================================
# 邮件通知配置
# =============================================================================
EMAIL_CONFIG = {
    "enabled": True,  # 是否启用邮件通知
    "smtp_server": "smtp.qq.com",  # SMTP服务器地址
    "smtp_port": 587,  # SMTP端口
    "sender_email": "",  # 发送者邮箱 - 请填写您的邮箱
    "sender_password": "",  # 邮箱授权码 - 请填写您的邮箱授权码
    "receiver_email": "",  # 接收者邮箱 - 请填写接收通知的邮箱
}

# 常用邮箱SMTP配置参考：
# QQ邮箱: smtp.qq.com:587
# 163邮箱: smtp.163.com:25
# Gmail: smtp.gmail.com:587
# Outlook: smtp-mail.outlook.com:587

# =============================================================================
# Server酱微信通知配置 (推荐)
# =============================================================================
SERVER_CHAN_CONFIG = {
    "enabled": True,  # 是否启用Server酱通知
    "send_key": "SCT292710TUZ7Y1BgnUxd9CJI8GGYq1tQo",  # Server酱的SendKey - 请到 https://sct.ftqq.com/ 获取
}

# Server酱使用说明：
# 1. 访问 https://sct.ftqq.com/
# 2. 使用微信扫码登录
# 3. 获取SendKey
# 4. 将SendKey填入上面的配置中

# =============================================================================
# 企业微信机器人配置
# =============================================================================
WECHAT_WORK_CONFIG = {
    "enabled": False,  # 是否启用企业微信通知
    "webhook_url": "",  # 企业微信机器人webhook地址
}

# 企业微信机器人使用说明：
# 1. 在企业微信群中添加机器人
# 2. 获取webhook地址
# 3. 将地址填入上面的配置中

# =============================================================================
# 本地通知配置
# =============================================================================
LOCAL_NOTIFICATION_CONFIG = {
    "sound_alert": True,  # 是否播放声音提醒
    "desktop_notification": True,  # 是否显示桌面通知
    "save_log": True,  # 是否保存日志文件
}

# =============================================================================
# 通知内容配置
# =============================================================================
NOTIFICATION_CONTENT_CONFIG = {
    "include_doctor_info": True,  # 是否包含医生信息
    "include_hospital_info": True,  # 是否包含医院信息
    "include_time_info": True,  # 是否包含时间信息
    "include_fee_info": True,  # 是否包含费用信息
}

# =============================================================================
# 高级配置
# =============================================================================
ADVANCED_CONFIG = {
    "notification_cooldown": 300,  # 通知冷却时间（秒），避免频繁通知
    "max_notifications_per_hour": 10,  # 每小时最大通知次数
    "quiet_hours": {  # 静默时间段（24小时制）
        "enabled": False,  # 是否启用静默时间
        "start_hour": 23,  # 静默开始时间
        "end_hour": 7,  # 静默结束时间
    }
}

# =============================================================================
# 配置验证函数
# =============================================================================
def validate_config():
    """验证配置是否完整"""
    issues = []
    
    # 检查邮件配置
    if EMAIL_CONFIG["enabled"]:
        if not EMAIL_CONFIG["sender_email"]:
            issues.append("邮件通知已启用，但未配置发送者邮箱")
        if not EMAIL_CONFIG["sender_password"]:
            issues.append("邮件通知已启用，但未配置邮箱授权码")
        if not EMAIL_CONFIG["receiver_email"]:
            issues.append("邮件通知已启用，但未配置接收者邮箱")
    
    # 检查Server酱配置
    if SERVER_CHAN_CONFIG["enabled"]:
        if not SERVER_CHAN_CONFIG["send_key"]:
            issues.append("Server酱通知已启用，但未配置SendKey")
    
    # 检查企业微信配置
    if WECHAT_WORK_CONFIG["enabled"]:
        if not WECHAT_WORK_CONFIG["webhook_url"]:
            issues.append("企业微信通知已启用，但未配置webhook地址")
    
    return issues

def print_config_status():
    """打印配置状态"""
    print("通知配置状态:")
    print(f"📧 邮件通知: {'✅ 已启用' if EMAIL_CONFIG['enabled'] else '❌ 已禁用'}")
    print(f"📱 Server酱微信通知: {'✅ 已启用' if SERVER_CHAN_CONFIG['enabled'] else '❌ 已禁用'}")
    print(f"💼 企业微信通知: {'✅ 已启用' if WECHAT_WORK_CONFIG['enabled'] else '❌ 已禁用'}")
    print(f"🔊 声音提醒: {'✅ 已启用' if LOCAL_NOTIFICATION_CONFIG['sound_alert'] else '❌ 已禁用'}")
    print(f"🖥️ 桌面通知: {'✅ 已启用' if LOCAL_NOTIFICATION_CONFIG['desktop_notification'] else '❌ 已禁用'}")
    
    issues = validate_config()
    if issues:
        print("\n⚠️ 配置问题:")
        for issue in issues:
            print(f"   - {issue}")
    else:
        print("\n✅ 配置检查通过")

if __name__ == "__main__":
    print_config_status()
