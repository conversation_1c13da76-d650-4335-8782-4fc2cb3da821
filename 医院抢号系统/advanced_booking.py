#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级医院挂号监控程序
支持多医生查询、定时监控、可用号源提醒等功能
"""

import requests
import json
import base64
import time
import schedule
from datetime import datetime
from typing import Dict, Any, Optional, List
from config import API_CONFIG, HEADERS, DEFAULT_PARAMS, DOCTOR_CODES


class AdvancedHospitalBooking:
    """高级医院挂号监控系统"""
    
    def __init__(self):
        self.base_url = API_CONFIG["base_url"]
        self.endpoint = API_CONFIG["endpoint"]
        self.timeout = API_CONFIG["timeout"]
        self.session = requests.Session()
        self.headers = HEADERS.copy()
        
    def get_doctor_info(self, doctor_code: str, **kwargs) -> Optional[Dict[str, Any]]:
        """获取医生挂号信息"""
        url = f"{self.base_url}{self.endpoint}"
        
        # 合并默认参数和传入参数
        params = DEFAULT_PARAMS.copy()
        params.update(kwargs)
        params["doctor_code"] = doctor_code
        
        data = {
            "yqcode1": params.get("yq_code1", ""),
            "kscode1": params.get("ks_code1", ""),
            "doctorCode": doctor_code,
            "scheduleType": params.get("schedule_type", ""),
            "laterThan17": str(params.get("later_than_17", True)).lower()
        }
        
        try:
            response = self.session.post(url, headers=self.headers, data=data, timeout=self.timeout)
            
            if response.status_code == 200:
                try:
                    return response.json()
                except json.JSONDecodeError:
                    try:
                        decoded_content = base64.b64decode(response.text).decode('utf-8')
                        return json.loads(decoded_content)
                    except Exception:
                        return None
            return None
            
        except requests.exceptions.RequestException:
            return None
    
    def check_available_slots(self, doctor_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """检查可用的挂号时段"""
        available_slots = []
        
        if not doctor_info or not doctor_info.get('success'):
            return available_slots
        
        # 检查datalist中的排班
        datalist = doctor_info.get('datalist', [])
        for hospital in datalist:
            schedules = hospital.get('schedule', [])
            for schedule in schedules:
                status = schedule.get('yystatus', '')
                if status in ['停诊', '截止']:
                    continue
                    
                available_slots.append({
                    'hospital': hospital.get('hospitalmc', ''),
                    'dept': schedule.get('deptName', ''),
                    'date': schedule.get('clinicDate', ''),
                    'time': schedule.get('clinicDuration', ''),
                    'fee': schedule.get('sumFee', ''),
                    'status': status,
                    'type': schedule.get('ghlx', ''),
                    'schedule_code': schedule.get('schedulecode', '')
                })
        
        # 检查datalistbyyq中的排班
        datalistbyyq = doctor_info.get('datalistbyyq', [])
        for hospital in datalistbyyq:
            schedules = hospital.get('schedule', [])
            for schedule in schedules:
                status = schedule.get('yystatus', '')
                tingzheng = schedule.get('tingzheng', False)
                
                if status in ['停诊', '截止'] or tingzheng:
                    continue
                    
                # 避免重复添加
                schedule_code = schedule.get('schedulecode', '')
                if not any(slot['schedule_code'] == schedule_code for slot in available_slots):
                    available_slots.append({
                        'hospital': hospital.get('hospitalmc', ''),
                        'dept': schedule.get('deptName', ''),
                        'date': schedule.get('clinicDate', ''),
                        'time': schedule.get('clinicDuration', ''),
                        'fee': schedule.get('sumFee', ''),
                        'status': status,
                        'type': schedule.get('ghlx', ''),
                        'schedule_code': schedule_code
                    })
        
        return available_slots
    
    def monitor_doctor(self, doctor_code: str, doctor_name: str = None) -> None:
        """监控单个医生的挂号情况"""
        if not doctor_name:
            doctor_name = f"医生{doctor_code}"
            
        print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 正在监控 {doctor_name} (代码: {doctor_code})")
        
        doctor_info = self.get_doctor_info(doctor_code)
        if not doctor_info:
            print(f"❌ 获取 {doctor_name} 信息失败")
            return
        
        available_slots = self.check_available_slots(doctor_info)
        
        if available_slots:
            print(f"🎉 发现 {doctor_name} 有 {len(available_slots)} 个可用时段:")
            for i, slot in enumerate(available_slots, 1):
                print(f"  {i}. {slot['hospital']} - {slot['dept']}")
                print(f"     日期: {slot['date']} {slot['time']}")
                print(f"     费用: {slot['fee']}元 | 状态: {slot['status']} | 类型: {slot['type']}")
                print()
            
            # 保存可用时段信息
            self.save_available_slots(doctor_name, doctor_code, available_slots)
        else:
            print(f"😔 {doctor_name} 暂无可用时段")
    
    def monitor_multiple_doctors(self, doctor_list: List[str]) -> None:
        """监控多个医生的挂号情况"""
        print(f"\n{'='*60}")
        print(f"开始监控 {len(doctor_list)} 位医生的挂号情况")
        print(f"{'='*60}")
        
        for doctor_code in doctor_list:
            doctor_name = None
            # 查找医生姓名
            for name, code in DOCTOR_CODES.items():
                if code == doctor_code:
                    doctor_name = name
                    break
            
            self.monitor_doctor(doctor_code, doctor_name)
            time.sleep(1)  # 避免请求过于频繁
    
    def save_available_slots(self, doctor_name: str, doctor_code: str, slots: List[Dict[str, Any]]) -> None:
        """保存可用时段信息到文件"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"医院抢号系统/available_slots_{doctor_code}_{timestamp}.json"
        
        data = {
            'doctor_name': doctor_name,
            'doctor_code': doctor_code,
            'timestamp': datetime.now().isoformat(),
            'available_slots': slots
        }
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"💾 可用时段信息已保存到: {filename}")
        except Exception as e:
            print(f"❌ 保存文件失败: {e}")
    
    def start_monitoring(self, doctor_list: List[str], interval_minutes: int = 5) -> None:
        """开始定时监控"""
        print(f"🚀 开始定时监控，每 {interval_minutes} 分钟检查一次")
        print("按 Ctrl+C 停止监控")
        
        # 设置定时任务
        schedule.every(interval_minutes).minutes.do(
            lambda: self.monitor_multiple_doctors(doctor_list)
        )
        
        # 立即执行一次
        self.monitor_multiple_doctors(doctor_list)
        
        try:
            while True:
                schedule.run_pending()
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n⏹️ 监控已停止")


def main():
    """主函数"""
    print("高级医院挂号监控程序")
    print("="*60)
    
    # 创建监控实例
    monitor = AdvancedHospitalBooking()
    
    # 要监控的医生列表
    doctors_to_monitor = ["100403"]  # 可以添加更多医生代码
    
    print("选择运行模式:")
    print("1. 单次查询")
    print("2. 定时监控")
    
    try:
        choice = input("请输入选择 (1 或 2): ").strip()
        
        if choice == "1":
            # 单次查询
            monitor.monitor_multiple_doctors(doctors_to_monitor)
        elif choice == "2":
            # 定时监控
            interval = input("请输入监控间隔(分钟，默认5): ").strip()
            interval = int(interval) if interval.isdigit() else 5
            monitor.start_monitoring(doctors_to_monitor, interval)
        else:
            print("无效选择，执行单次查询")
            monitor.monitor_multiple_doctors(doctors_to_monitor)
            
    except KeyboardInterrupt:
        print("\n程序已退出")
    except Exception as e:
        print(f"程序运行出错: {e}")


if __name__ == "__main__":
    main()
