#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医院挂号信息获取程序
基于Charles抓包的HAR文件分析编写
支持定时监控和多种通知方式
"""

import requests
import json
import base64
import time
import schedule
import os
import platform
from datetime import datetime
from typing import Dict, Any, Optional, List


class NotificationConfig:
    """通知配置类"""

    # 邮件配置
    EMAIL_CONFIG = {
        "smtp_server": "smtp.qq.com",  # QQ邮箱SMTP服务器
        "smtp_port": 587,
        "sender_email": "",  # 发送者邮箱，需要填写
        "sender_password": "",  # 邮箱授权码，需要填写
        "receiver_email": "",  # 接收者邮箱，需要填写
    }

    # Server酱配置（微信通知）
    SERVER_CHAN_CONFIG = {
        "send_key": "",  # Server酱的SendKey，需要填写
        "url": "https://sctapi.ftqq.com/{}.send"
    }

    # 企业微信机器人配置
    WECHAT_WORK_CONFIG = {
        "webhook_url": "",  # 企业微信机器人webhook地址，需要填写
    }


class NotificationManager:
    """通知管理器"""

    def __init__(self, config: NotificationConfig):
        self.config = config

    def send_email(self, subject: str, content: str) -> bool:
        """发送邮件通知"""
        try:
            import smtplib
            from email.mime.text import MimeText
            from email.mime.multipart import MimeMultipart

            email_config = self.config.EMAIL_CONFIG
            if not all([email_config["sender_email"], email_config["sender_password"],
                       email_config["receiver_email"]]):
                print("❌ 邮件配置不完整，跳过邮件通知")
                return False

            msg = MimeMultipart()
            msg['From'] = email_config["sender_email"]
            msg['To'] = email_config["receiver_email"]
            msg['Subject'] = subject

            msg.attach(MimeText(content, 'plain', 'utf-8'))

            server = smtplib.SMTP(email_config["smtp_server"], email_config["smtp_port"])
            server.starttls()
            server.login(email_config["sender_email"], email_config["sender_password"])
            server.send_message(msg)
            server.quit()

            print("✅ 邮件通知发送成功")
            return True
        except Exception as e:
            print(f"❌ 邮件发送失败: {e}")
            return False

    def send_server_chan(self, title: str, content: str) -> bool:
        """通过Server酱发送微信通知"""
        try:
            send_key = self.config.SERVER_CHAN_CONFIG["send_key"]
            if not send_key:
                print("❌ Server酱配置不完整，跳过微信通知")
                return False

            url = self.config.SERVER_CHAN_CONFIG["url"].format(send_key)
            data = {
                "title": title,
                "desp": content
            }

            response = requests.post(url, data=data, timeout=10)
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    print("✅ 微信通知发送成功")
                    return True

            print("❌ 微信通知发送失败")
            return False
        except Exception as e:
            print(f"❌ 微信通知发送异常: {e}")
            return False

    def send_wechat_work(self, content: str) -> bool:
        """发送企业微信通知"""
        try:
            webhook_url = self.config.WECHAT_WORK_CONFIG["webhook_url"]
            if not webhook_url:
                print("❌ 企业微信配置不完整，跳过企业微信通知")
                return False

            data = {
                "msgtype": "text",
                "text": {
                    "content": content
                }
            }

            response = requests.post(webhook_url, json=data, timeout=10)
            if response.status_code == 200:
                result = response.json()
                if result.get("errcode") == 0:
                    print("✅ 企业微信通知发送成功")
                    return True

            print("❌ 企业微信通知发送失败")
            return False
        except Exception as e:
            print(f"❌ 企业微信通知发送异常: {e}")
            return False

    def play_sound_alert(self) -> bool:
        """播放声音提醒"""
        try:
            system = platform.system()
            if system == "Darwin":  # macOS
                os.system("afplay /System/Library/Sounds/Glass.aiff 2>/dev/null || echo '🔊 声音提醒'")
            elif system == "Windows":
                # 使用简单的echo命令作为提醒
                print("🔊 Windows声音提醒：发现可用号源！")
            elif system == "Linux":
                os.system("paplay /usr/share/sounds/alsa/Front_Left.wav 2>/dev/null || echo '🔊 声音提醒：发现可用号源！'")

            print("🔊 声音提醒已播放")
            return True
        except Exception as e:
            print(f"❌ 声音提醒失败: {e}")
            return False

    def show_desktop_notification(self, title: str, message: str) -> bool:
        """显示桌面通知"""
        try:
            system = platform.system()
            if system == "Darwin":  # macOS
                # 转义引号以避免命令注入
                safe_title = title.replace('"', '\\"').replace("'", "\\'")
                safe_message = message.replace('"', '\\"').replace("'", "\\'")
                os.system(f'osascript -e \'display notification "{safe_message}" with title "{safe_title}"\'')
            elif system == "Windows":
                try:
                    import win10toast
                    toaster = win10toast.ToastNotifier()
                    toaster.show_toast(title, message, duration=10)
                except ImportError:
                    print("Windows桌面通知模块不可用")
            elif system == "Linux":
                # 转义引号以避免命令注入
                safe_title = title.replace('"', '\\"')
                safe_message = message.replace('"', '\\"')
                os.system(f'notify-send "{safe_title}" "{safe_message}" 2>/dev/null || echo "桌面通知：{title}"')

            print("🖥️ 桌面通知已显示")
            return True
        except Exception as e:
            print(f"❌ 桌面通知失败: {e}")
            return False

    def save_alert_log(self, content: str) -> bool:
        """保存提醒日志"""
        try:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            log_content = f"[{timestamp}] {content}\n"

            with open('医院抢号系统/alert_log.txt', 'a', encoding='utf-8') as f:
                f.write(log_content)

            print("📝 提醒日志已保存")
            return True
        except Exception as e:
            print(f"❌ 保存日志失败: {e}")
            return False

    def send_all_notifications(self, title: str, content: str) -> None:
        """发送所有配置的通知"""
        print(f"\n🚨 发现可用号源，正在发送通知...")

        # 发送各种通知
        self.send_email(title, content)
        self.send_server_chan(title, content)
        self.send_wechat_work(content)
        self.play_sound_alert()
        self.show_desktop_notification(title, content)
        self.save_alert_log(content)


class HospitalBookingAPI:
    """医院挂号API客户端"""

    def __init__(self):
        self.base_url = "https://tjhapp.com.cn:8013"
        self.session = requests.Session()
        self.notification_manager = NotificationManager(NotificationConfig())
        self.last_available_slots = set()  # 记录上次的可用时段，避免重复通知

        # 从HAR文件中提取的请求头
        self.headers = {
            "Host": "tjhapp.com.cn:8013",
            "uuid": "oqrz4jiS2A2OMaAbk5p7RX9fHd9M",
            "Accept": "application/json",
            "X-Requested-With": "XMLHttpRequest",
            "ukey": "0145409357c309ff171da9d53b949432",
            "uname": "13971563836",
            "Accept-Language": "zh-CN,zh-Hans;q=0.9",
            "token": "eJwB0AAv_2-_7yxsSC2zKv9ZTAe7L_QDj25_afNX4bqwDWEknhvFfOW8b3KTX0auG4hr4mDslFMtaZL-aAbKtTTCctdLSsBa32rH2Nz15UcqSUuqiiVZv0E4arCO06qFYcJ1rwtR36RrcrzZXbI7lucFS45290qtaTj2T4QEeoFfW0luhmE9w6w479XcBgxA5wd1Y-ZJtuoxvmAMcMnGhHm_lTXKeoXvhSid6o4HaJ28Szt5B8vV4w_kltITXch7lPqP14GifpPWC7YXVmVCrks3yTtKZ14D91FokTI=",
            "Accept-Encoding": "gzip, deflate, br",
            "Origin": "https://tjhapp.com.cn",
            "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 15_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.60(0x18003c32) NetType/WIFI Language/zh_CN",
            "Referer": "https://tjhapp.com.cn/",
            "plan": "wxapp",
            "Connection": "keep-alive",
            "Content-Type": "application/x-www-form-urlencoded"
        }
    
    def get_doctor_info(self, doctor_code: str = "100403", 
                       yq_code1: str = "", 
                       ks_code1: str = "", 
                       schedule_type: str = "", 
                       later_than_17: bool = True) -> Optional[Dict[str, Any]]:
        """
        获取医生挂号信息
        
        Args:
            doctor_code: 医生代码，默认为"100403"
            yq_code1: 院区代码1
            ks_code1: 科室代码1  
            schedule_type: 排班类型
            later_than_17: 是否17点后，默认True
            
        Returns:
            返回医生挂号信息的字典，失败返回None
        """
        url = f"{self.base_url}/yuyue/getdocinfoNewV2"
        
        # POST数据
        data = {
            "yqcode1": yq_code1,
            "kscode1": ks_code1,
            "doctorCode": doctor_code,
            "scheduleType": schedule_type,
            "laterThan17": str(later_than_17).lower()
        }
        
        try:
            print(f"正在请求医生信息，医生代码: {doctor_code}")
            response = self.session.post(url, headers=self.headers, data=data, timeout=30)
            
            if response.status_code == 200:
                print("请求成功！")
                
                # 尝试解析JSON响应
                try:
                    result = response.json()
                    return result
                except json.JSONDecodeError:
                    # 如果不是直接的JSON，可能是base64编码的
                    try:
                        decoded_content = base64.b64decode(response.text).decode('utf-8')
                        result = json.loads(decoded_content)
                        return result
                    except Exception as e:
                        print(f"解码响应失败: {e}")
                        return None
            else:
                print(f"请求失败，状态码: {response.status_code}")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"网络请求异常: {e}")
            return None

    def check_target_slots(self, data: Dict[str, Any], target_fee: float = 26.5) -> List[Dict[str, Any]]:
        """
        检查目标价格的可用号源

        Args:
            data: API返回的数据字典
            target_fee: 目标挂号费，默认26.5元

        Returns:
            符合条件的可用时段列表
        """
        available_slots = []

        if not data or not data.get('success'):
            return available_slots

        # 检查datalist中的排班
        datalist = data.get('datalist', [])
        for hospital in datalist:
            schedules = hospital.get('schedule', [])
            for schedule in schedules:
                fee = schedule.get('sumFee', 0)
                status = schedule.get('yystatus', '')

                # 检查是否是目标价格且未约满
                if (fee == target_fee and
                    status not in ['停诊', '截止', '约满'] and
                    '约满' not in status):

                    slot_info = {
                        'hospital': hospital.get('hospitalmc', ''),
                        'dept': schedule.get('deptName', ''),
                        'date': schedule.get('clinicDate', ''),
                        'time': schedule.get('clinicDuration', ''),
                        'fee': fee,
                        'status': status,
                        'type': schedule.get('ghlx', ''),
                        'schedule_code': schedule.get('schedulecode', ''),
                        'doctor_code': schedule.get('doctorCode', ''),
                        'paiban_id': schedule.get('paibanid', '')
                    }
                    available_slots.append(slot_info)

        # 检查datalistbyyq中的排班
        datalistbyyq = data.get('datalistbyyq', [])
        for hospital in datalistbyyq:
            schedules = hospital.get('schedule', [])
            for schedule in schedules:
                fee = schedule.get('sumFee', 0)
                status = schedule.get('yystatus', '')
                tingzheng = schedule.get('tingzheng', False)

                # 检查是否是目标价格且未约满且未停诊
                if (fee == target_fee and
                    status not in ['停诊', '截止', '约满'] and
                    '约满' not in status and
                    not tingzheng):

                    schedule_code = schedule.get('schedulecode', '')
                    # 避免重复添加
                    if not any(slot['schedule_code'] == schedule_code for slot in available_slots):
                        slot_info = {
                            'hospital': hospital.get('hospitalmc', ''),
                            'dept': schedule.get('deptName', ''),
                            'date': schedule.get('clinicDate', ''),
                            'time': schedule.get('clinicDuration', ''),
                            'fee': fee,
                            'status': status,
                            'type': schedule.get('ghlx', ''),
                            'schedule_code': schedule_code,
                            'doctor_code': schedule.get('doctorCode', ''),
                            'paiban_id': schedule.get('paibanid', '')
                        }
                        available_slots.append(slot_info)

        return available_slots

    def monitor_and_notify(self, doctor_code: str = "100403", target_fee: float = 26.5) -> None:
        """
        监控并通知目标价格的可用号源

        Args:
            doctor_code: 医生代码
            target_fee: 目标挂号费
        """
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"\n[{current_time}] 正在监控医生 {doctor_code} 的 {target_fee}元 号源...")

        try:
            # 获取医生信息
            doctor_info = self.get_doctor_info(doctor_code)
            if not doctor_info:
                print(f"❌ 获取医生信息失败")
                return

            # 检查目标价格的可用号源
            available_slots = self.check_target_slots(doctor_info, target_fee)

            if available_slots:
                # 生成当前可用时段的唯一标识
                current_slots_ids = set(slot['schedule_code'] for slot in available_slots)

                # 检查是否有新的可用时段
                new_slots = current_slots_ids - self.last_available_slots

                if new_slots:
                    # 有新的可用时段，发送通知
                    doctor_name = doctor_info.get('datainfo', {}).get('doctorName', f'医生{doctor_code}')

                    # 构建通知内容
                    title = f"🎉 发现{target_fee}元可用号源！"
                    content = f"医生：{doctor_name}\n"
                    content += f"时间：{current_time}\n\n"

                    for slot in available_slots:
                        if slot['schedule_code'] in new_slots:
                            content += f"📅 {slot['hospital']} - {slot['dept']}\n"
                            content += f"   日期：{slot['date']} {slot['time']}\n"
                            content += f"   费用：{slot['fee']}元\n"
                            content += f"   状态：{slot['status']}\n"
                            content += f"   类型：{slot['type']}\n\n"

                    # 发送通知
                    self.notification_manager.send_all_notifications(title, content)

                    # 保存详细信息到文件
                    self.save_available_slots_detail(doctor_name, doctor_code, available_slots)

                # 更新记录的可用时段
                self.last_available_slots = current_slots_ids

                print(f"✅ 发现 {len(available_slots)} 个 {target_fee}元 可用时段")
                for slot in available_slots:
                    print(f"   📅 {slot['date']} {slot['time']} - {slot['hospital']} ({slot['status']})")
            else:
                print(f"😔 暂无 {target_fee}元 可用号源")
                # 清空记录，为下次检测做准备
                self.last_available_slots.clear()

        except Exception as e:
            print(f"❌ 监控过程中出现异常: {e}")

    def save_available_slots_detail(self, doctor_name: str, doctor_code: str, slots: List[Dict[str, Any]]) -> None:
        """保存可用时段详细信息"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"医院抢号系统/available_slots_{doctor_code}_{timestamp}.json"

        data = {
            'doctor_name': doctor_name,
            'doctor_code': doctor_code,
            'timestamp': datetime.now().isoformat(),
            'available_slots': slots,
            'alert_triggered': True
        }

        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"💾 详细信息已保存到: {filename}")
        except Exception as e:
            print(f"❌ 保存文件失败: {e}")

    def start_monitoring(self, doctor_code: str = "100403", target_fee: float = 26.5, interval_minutes: int = 1) -> None:
        """
        开始定时监控

        Args:
            doctor_code: 医生代码
            target_fee: 目标挂号费
            interval_minutes: 监控间隔（分钟）
        """
        print(f"🚀 开始定时监控医生 {doctor_code} 的 {target_fee}元 号源")
        print(f"⏰ 监控间隔: {interval_minutes} 分钟")
        print("按 Ctrl+C 停止监控\n")

        # 设置定时任务
        schedule.every(interval_minutes).minutes.do(
            lambda: self.monitor_and_notify(doctor_code, target_fee)
        )

        # 立即执行一次
        self.monitor_and_notify(doctor_code, target_fee)

        try:
            while True:
                schedule.run_pending()
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n⏹️ 监控已停止")

    def parse_doctor_info(self, data: Dict[str, Any]) -> None:
        """
        解析并打印医生信息
        
        Args:
            data: API返回的数据字典
        """
        if not data or not data.get('success'):
            print("获取医生信息失败")
            return
        
        datainfo = data.get('datainfo', {})
        
        print("\n" + "="*50)
        print("医生基本信息")
        print("="*50)
        print(f"医生姓名: {datainfo.get('doctorName', 'N/A')}")
        print(f"医生代码: {datainfo.get('doctorCode', 'N/A')}")
        print(f"医生职称: {datainfo.get('doctorTitle', 'N/A')}")
        print(f"科室名称: {datainfo.get('deptNames', 'N/A')}")
        print(f"医生简介: {datainfo.get('doctorDesc', 'N/A')[:100]}...")
        
        # 解析排班信息
        datalist = data.get('datalist', [])
        if datalist:
            print("\n" + "="*50)
            print("排班信息")
            print("="*50)
            
            for hospital in datalist:
                hospital_name = hospital.get('hospitalmc', 'N/A')
                print(f"\n医院: {hospital_name}")
                print("-" * 30)
                
                schedules = hospital.get('schedule', [])
                for schedule in schedules:
                    print(f"科室: {schedule.get('deptName', 'N/A')}")
                    print(f"就诊日期: {schedule.get('clinicDate', 'N/A')}")
                    print(f"就诊时间: {schedule.get('clinicDuration', 'N/A')}")
                    print(f"挂号费: {schedule.get('sumFee', 'N/A')}元")
                    print(f"预约状态: {schedule.get('yystatus', 'N/A')}")
                    print(f"挂号类型: {schedule.get('ghlx', 'N/A')}")
                    print("-" * 20)
        
        # 解析按院区分组的排班信息
        datalistbyyq = data.get('datalistbyyq', [])
        if datalistbyyq:
            print("\n" + "="*50)
            print("按院区分组的排班信息")
            print("="*50)
            
            for hospital in datalistbyyq:
                hospital_name = hospital.get('hospitalmc', 'N/A')
                print(f"\n医院: {hospital_name}")
                print("-" * 30)
                
                schedules = hospital.get('schedule', [])
                for schedule in schedules:
                    print(f"科室: {schedule.get('deptName', 'N/A')}")
                    print(f"就诊日期: {schedule.get('clinicDate', 'N/A')}")
                    print(f"就诊时间: {schedule.get('clinicDuration', 'N/A')}")
                    print(f"挂号费: {schedule.get('sumFee', 'N/A')}元")
                    print(f"预约状态: {schedule.get('yystatus', 'N/A')}")
                    print(f"挂号类型: {schedule.get('ghlx', 'N/A')}")
                    print(f"是否停诊: {'是' if schedule.get('tingzheng') else '否'}")
                    print("-" * 20)


def main():
    """主函数"""
    print("医院挂号信息获取程序 - 定时监控版")
    print("="*60)

    # 创建API客户端
    api = HospitalBookingAPI()

    print("选择运行模式:")
    print("1. 单次查询医生信息")
    print("2. 定时监控26.5元号源（推荐）")
    print("3. 自定义价格监控")

    try:
        choice = input("\n请输入选择 (1/2/3): ").strip()

        if choice == "1":
            # 单次查询
            print("\n正在查询医生信息...")
            doctor_info = api.get_doctor_info()

            if doctor_info:
                api.parse_doctor_info(doctor_info)

                # 保存原始数据到文件
                with open('医院抢号系统/doctor_info_raw.json', 'w', encoding='utf-8') as f:
                    json.dump(doctor_info, f, ensure_ascii=False, indent=2)
                print(f"\n原始数据已保存到: doctor_info_raw.json")
            else:
                print("获取医生信息失败")

        elif choice == "2":
            # 定时监控26.5元号源
            doctor_code = input("请输入医生代码 (默认100403): ").strip() or "100403"
            interval = input("请输入监控间隔(分钟，默认1): ").strip()
            interval = int(interval) if interval.isdigit() else 1

            print(f"\n⚠️  重要提醒:")
            print("1. 请确保在 config.py 或代码中配置了通知方式")
            print("2. 建议配置邮件通知或Server酱微信通知")
            print("3. 程序会在发现26.5元可用号源时立即通知您")
            print("4. 通知包括：邮件、微信、声音、桌面弹窗、日志文件")

            confirm = input("\n是否开始监控? (y/n): ").strip().lower()
            if confirm in ['y', 'yes', '是']:
                api.start_monitoring(doctor_code, 26.5, interval)
            else:
                print("已取消监控")

        elif choice == "3":
            # 自定义价格监控
            doctor_code = input("请输入医生代码 (默认100403): ").strip() or "100403"
            target_fee = input("请输入目标挂号费 (默认26.5): ").strip()
            target_fee = float(target_fee) if target_fee.replace('.', '').isdigit() else 26.5
            interval = input("请输入监控间隔(分钟，默认1): ").strip()
            interval = int(interval) if interval.isdigit() else 1

            print(f"\n将监控医生 {doctor_code} 的 {target_fee}元 号源")
            confirm = input("是否开始监控? (y/n): ").strip().lower()
            if confirm in ['y', 'yes', '是']:
                api.start_monitoring(doctor_code, target_fee, interval)
            else:
                print("已取消监控")
        else:
            print("无效选择，执行单次查询")
            doctor_info = api.get_doctor_info()
            if doctor_info:
                api.parse_doctor_info(doctor_info)

    except KeyboardInterrupt:
        print("\n程序已退出")
    except Exception as e:
        print(f"程序运行出错: {e}")


if __name__ == "__main__":
    main()
