#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医院挂号信息获取程序
基于Charles抓包的HAR文件分析编写
支持定时监控和多种通知方式
"""

import requests
import json
import base64
import time
import schedule
from datetime import datetime
from typing import Dict, Any, Optional, List
from config import MONITOR_CONFIG, DEFAULT_PARAMS, HEADERS


class NotificationConfig:
    """通知配置类"""

    # 邮件配置
    EMAIL_CONFIG = {
        "enabled": False,  # 默认禁用邮件通知
        "smtp_server": "smtp.qq.com",
        "smtp_port": 587,
        "sender_email": "",
        "sender_password": "",
        "receiver_email": "",
    }

    # Server酱配置（微信通知）
    SERVER_CHAN_CONFIG = {
        "enabled": True,  # 启用微信通知
        "send_key": "SCT292710TUZ7Y1BgnUxd9CJI8GGYq1tQo",
        "url": "https://sctapi.ftqq.com/{}.send"
    }


class NotificationManager:
    """通知管理器"""

    def __init__(self, config: NotificationConfig):
        self.config = config

    def send_email(self, subject: str, content: str) -> bool:
        """发送邮件通知"""
        try:
            import smtplib
            from email.mime.text import MimeText
            from email.mime.multipart import MimeMultipart

            email_config = self.config.EMAIL_CONFIG
            if not all([email_config["sender_email"], email_config["sender_password"],
                       email_config["receiver_email"]]):
                print("❌ 邮件配置不完整，跳过邮件通知")
                return False

            msg = MimeMultipart()
            msg['From'] = email_config["sender_email"]
            msg['To'] = email_config["receiver_email"]
            msg['Subject'] = subject

            msg.attach(MimeText(content, 'plain', 'utf-8'))

            server = smtplib.SMTP(email_config["smtp_server"], email_config["smtp_port"])
            server.starttls()
            server.login(email_config["sender_email"], email_config["sender_password"])
            server.send_message(msg)
            server.quit()

            print("✅ 邮件通知发送成功")
            return True
        except Exception as e:
            print(f"❌ 邮件发送失败: {e}")
            return False

    def send_server_chan(self, title: str, content: str) -> bool:
        """通过Server酱发送微信通知"""
        try:
            send_key = self.config.SERVER_CHAN_CONFIG["send_key"]
            if not send_key:
                print("❌ Server酱配置不完整，跳过微信通知")
                return False

            url = self.config.SERVER_CHAN_CONFIG["url"].format(send_key)
            data = {
                "title": title,
                "desp": content
            }

            response = requests.post(url, data=data, timeout=10)
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    print("✅ 微信通知发送成功")
                    return True

            print("❌ 微信通知发送失败")
            return False
        except Exception as e:
            print(f"❌ 微信通知发送异常: {e}")
            return False



    def save_alert_log(self, content: str) -> bool:
        """保存提醒日志"""
        try:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            log_content = f"[{timestamp}] {content}\n"

            with open('医院抢号系统/alert_log.txt', 'a', encoding='utf-8') as f:
                f.write(log_content)

            print("📝 提醒日志已保存")
            return True
        except Exception as e:
            print(f"❌ 保存日志失败: {e}")
            return False

    def send_all_notifications(self, title: str, content: str) -> None:
        """发送所有配置的通知"""
        print(f"\n🚨 发现可用号源，正在发送通知...")

        # 发送微信和邮件通知
        self.send_server_chan(title, content)
        self.send_email(title, content)
        self.save_alert_log(content)


class HospitalBookingAPI:
    """医院挂号API客户端"""

    def __init__(self):
        self.base_url = "https://tjhapp.com.cn:8013"
        self.session = requests.Session()
        self.notification_manager = NotificationManager(NotificationConfig())
        self.last_available_slots = set()  # 记录上次的可用时段，避免重复通知

        # 从config.py导入请求头
        self.headers = HEADERS
    
    def get_doctor_info(self, doctor_code: str = None) -> Optional[Dict[str, Any]]:
        """
        获取医生挂号信息

        Args:
            doctor_code: 医生代码，如果为None则使用配置文件中的默认值

        Returns:
            返回医生挂号信息的字典，失败返回None
        """
        if doctor_code is None:
            doctor_code = MONITOR_CONFIG["doctor_code"]

        url = f"{self.base_url}/yuyue/getdocinfoNewV2"

        # POST数据
        data = {
            "yqcode1": DEFAULT_PARAMS["yq_code1"],
            "kscode1": DEFAULT_PARAMS["ks_code1"],
            "doctorCode": doctor_code,
            "scheduleType": DEFAULT_PARAMS["schedule_type"],
            "laterThan17": str(DEFAULT_PARAMS["later_than_17"]).lower()
        }
        
        try:
            print(f"正在请求医生信息，医生代码: {doctor_code}")
            response = self.session.post(url, headers=self.headers, data=data, timeout=30)
            
            if response.status_code == 200:
                print("请求成功！")
                
                # 尝试解析JSON响应
                try:
                    result = response.json()
                    return result
                except json.JSONDecodeError:
                    # 如果不是直接的JSON，可能是base64编码的
                    try:
                        decoded_content = base64.b64decode(response.text).decode('utf-8')
                        result = json.loads(decoded_content)
                        return result
                    except Exception as e:
                        print(f"解码响应失败: {e}")
                        return None
            else:
                print(f"请求失败，状态码: {response.status_code}")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"网络请求异常: {e}")
            return None

    def check_affordable_slots(self, data: Dict[str, Any], max_fee: float = None) -> List[Dict[str, Any]]:
        """
        检查指定价格以内的可用号源

        Args:
            data: API返回的数据字典
            max_fee: 最大挂号费，如果为None则使用配置文件中的值

        Returns:
            符合条件的可用时段列表
        """
        if max_fee is None:
            max_fee = MONITOR_CONFIG["max_fee"]
        available_slots = []

        if not data or not data.get('success'):
            return available_slots

        # 检查datalist中的排班
        datalist = data.get('datalist', [])
        for hospital in datalist:
            schedules = hospital.get('schedule', [])
            for schedule in schedules:
                fee = schedule.get('sumFee', 0)
                status = schedule.get('yystatus', '')

                # 检查是否在价格范围内且未约满
                if (fee <= max_fee and
                    status not in ['停诊', '截止', '约满'] and
                    '约满' not in status):

                    slot_info = {
                        'hospital': hospital.get('hospitalmc', ''),
                        'dept': schedule.get('deptName', ''),
                        'date': schedule.get('clinicDate', ''),
                        'time': schedule.get('clinicDuration', ''),
                        'fee': fee,
                        'status': status,
                        'type': schedule.get('ghlx', ''),
                        'schedule_code': schedule.get('schedulecode', ''),
                        'doctor_code': schedule.get('doctorCode', ''),
                        'paiban_id': schedule.get('paibanid', '')
                    }
                    available_slots.append(slot_info)

        # 检查datalistbyyq中的排班
        datalistbyyq = data.get('datalistbyyq', [])
        for hospital in datalistbyyq:
            schedules = hospital.get('schedule', [])
            for schedule in schedules:
                fee = schedule.get('sumFee', 0)
                status = schedule.get('yystatus', '')
                tingzheng = schedule.get('tingzheng', False)

                # 检查是否在价格范围内且未约满且未停诊
                if (fee <= max_fee and
                    status not in ['停诊', '截止', '约满'] and
                    '约满' not in status and
                    not tingzheng):

                    schedule_code = schedule.get('schedulecode', '')
                    # 避免重复添加
                    if not any(slot['schedule_code'] == schedule_code for slot in available_slots):
                        slot_info = {
                            'hospital': hospital.get('hospitalmc', ''),
                            'dept': schedule.get('deptName', ''),
                            'date': schedule.get('clinicDate', ''),
                            'time': schedule.get('clinicDuration', ''),
                            'fee': fee,
                            'status': status,
                            'type': schedule.get('ghlx', ''),
                            'schedule_code': schedule_code,
                            'doctor_code': schedule.get('doctorCode', ''),
                            'paiban_id': schedule.get('paibanid', '')
                        }
                        available_slots.append(slot_info)

        return available_slots

    def monitor_and_notify(self, doctor_code: str = None, max_fee: float = None) -> None:
        """
        监控并通知指定价格以内的可用号源

        Args:
            doctor_code: 医生代码，如果为None则使用配置文件中的值
            max_fee: 最大挂号费，如果为None则使用配置文件中的值
        """
        if doctor_code is None:
            doctor_code = MONITOR_CONFIG["doctor_code"]
        if max_fee is None:
            max_fee = MONITOR_CONFIG["max_fee"]

        doctor_name = MONITOR_CONFIG["doctor_name"]
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"\n[{current_time}] 正在监控 {doctor_name}({doctor_code}) 的 {max_fee}元以内号源...")

        try:
            # 获取医生信息
            doctor_info = self.get_doctor_info(doctor_code)
            if not doctor_info:
                print(f"❌ 获取医生信息失败")
                return

            # 检查指定价格以内的可用号源
            available_slots = self.check_affordable_slots(doctor_info, max_fee)

            if available_slots:
                # 生成当前可用时段的唯一标识
                current_slots_ids = set(slot['schedule_code'] for slot in available_slots)

                # 检查是否有新的可用时段
                new_slots = current_slots_ids - self.last_available_slots

                if new_slots:
                    # 有新的可用时段，发送通知
                    doctor_name = doctor_info.get('datainfo', {}).get('doctorName', f'医生{doctor_code}')

                    # 构建通知内容
                    title = f"🎉 发现{max_fee}元以内可用号源！"
                    content = f"医生：{doctor_name}\n"
                    content += f"时间：{current_time}\n\n"

                    for slot in available_slots:
                        if slot['schedule_code'] in new_slots:
                            content += f"📅 {slot['hospital']} - {slot['dept']}\n"
                            content += f"   日期：{slot['date']} {slot['time']}\n"
                            content += f"   费用：{slot['fee']}元\n"
                            content += f"   状态：{slot['status']}\n"
                            content += f"   类型：{slot['type']}\n\n"

                    # 发送通知
                    self.notification_manager.send_all_notifications(title, content)

                    # 保存详细信息到文件
                    self.save_available_slots_detail(doctor_name, doctor_code, available_slots)

                # 更新记录的可用时段
                self.last_available_slots = current_slots_ids

                print(f"✅ 发现 {len(available_slots)} 个 {max_fee}元以内可用时段")
                for slot in available_slots:
                    print(f"   📅 {slot['date']} {slot['time']} - {slot['hospital']} {slot['fee']}元 ({slot['status']})")
            else:
                print(f"😔 暂无 {max_fee}元以内可用号源")
                # 清空记录，为下次检测做准备
                self.last_available_slots.clear()

        except Exception as e:
            print(f"❌ 监控过程中出现异常: {e}")

    def save_available_slots_detail(self, doctor_name: str, doctor_code: str, slots: List[Dict[str, Any]]) -> None:
        """保存可用时段详细信息"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"医院抢号系统/available_slots_{doctor_code}_{timestamp}.json"

        data = {
            'doctor_name': doctor_name,
            'doctor_code': doctor_code,
            'timestamp': datetime.now().isoformat(),
            'available_slots': slots,
            'alert_triggered': True
        }

        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"💾 详细信息已保存到: {filename}")
        except Exception as e:
            print(f"❌ 保存文件失败: {e}")

    def start_monitoring(self, doctor_code: str = None, max_fee: float = None, interval_minutes: int = None) -> None:
        """
        开始定时监控

        Args:
            doctor_code: 医生代码，如果为None则使用配置文件中的值
            max_fee: 最大挂号费，如果为None则使用配置文件中的值
            interval_minutes: 监控间隔（分钟），如果为None则使用配置文件中的值
        """
        if doctor_code is None:
            doctor_code = MONITOR_CONFIG["doctor_code"]
        if max_fee is None:
            max_fee = MONITOR_CONFIG["max_fee"]
        if interval_minutes is None:
            interval_minutes = MONITOR_CONFIG["monitor_interval"]

        doctor_name = MONITOR_CONFIG["doctor_name"]
        print(f"🚀 开始定时监控 {doctor_name}({doctor_code}) 的 {max_fee}元以内号源")
        print(f"⏰ 监控间隔: {interval_minutes} 分钟")
        print("按 Ctrl+C 停止监控\n")

        # 设置定时任务
        schedule.every(interval_minutes).minutes.do(
            lambda: self.monitor_and_notify(doctor_code, max_fee)
        )

        # 立即执行一次
        self.monitor_and_notify(doctor_code, max_fee)

        try:
            while True:
                schedule.run_pending()
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n⏹️ 监控已停止")

def main():
    """主函数"""
    print("医院挂号信息获取程序 - 定时监控版")
    print("="*60)

    # 显示当前配置
    doctor_name = MONITOR_CONFIG["doctor_name"]
    doctor_code = MONITOR_CONFIG["doctor_code"]
    max_fee = MONITOR_CONFIG["max_fee"]
    interval = MONITOR_CONFIG["monitor_interval"]

    print(f"当前配置:")
    print(f"  医生: {doctor_name} (代码: {doctor_code})")
    print(f"  价格范围: {max_fee}元以内")
    print(f"  监控间隔: {interval}分钟")
    print(f"\n如需修改配置，请编辑 config.py 文件")

    # 创建API客户端
    api = HospitalBookingAPI()

    print("\n选择运行模式:")
    print("1. 单次查询医生信息")
    print("2. 开始定时监控（推荐）")

    try:
        choice = input("\n请输入选择 (1/2): ").strip()

        if choice == "1":
            # 单次查询
            print(f"\n正在查询 {doctor_name} 的挂号信息...")
            doctor_info = api.get_doctor_info()

            if doctor_info:
                # 检查可用号源
                available_slots = api.check_affordable_slots(doctor_info)

                if available_slots:
                    print(f"\n✅ 发现 {len(available_slots)} 个 {max_fee}元以内的时段:")
                    for slot in available_slots:
                        print(f"   📅 {slot['date']} {slot['time']} - {slot['hospital']}")
                        print(f"      费用: {slot['fee']}元 | 状态: {slot['status']} | 类型: {slot['type']}")
                else:
                    print(f"\n😔 暂无 {max_fee}元以内的可用号源")

            else:
                print("获取医生信息失败")

        elif choice == "2":
            # 开始定时监控
            print(f"\n⚠️  重要提醒:")
            print("1. 微信通知已配置，发现可用号源时会立即通知")
            print("2. 程序会持续运行，按 Ctrl+C 停止监控")
            print("3. 监控日志会保存到 alert_log.txt 文件")

            confirm = input("\n是否开始监控? (y/n): ").strip().lower()
            if confirm in ['y', 'yes', '是']:
                api.start_monitoring()
            else:
                print("已取消监控")
        else:
            print("无效选择，执行单次查询")
            api.monitor_and_notify()

    except KeyboardInterrupt:
        print("\n程序已退出")
    except Exception as e:
        print(f"程序运行出错: {e}")


if __name__ == "__main__":
    main()
