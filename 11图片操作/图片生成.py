# from PIL import Image, ImageDraw
#
#
# def create_circle(scale, size_pt=13):
#     # 将pt转换为px (1pt = scale px)
#     size_px = int(size_pt * scale)
#     # 线宽 (0.5pt * scale)
#     line_width = 0.5 * scale
#
#     # 创建透明背景的图片
#     image = Image.new('RGBA', (size_px, size_px), (0, 0, 0, 0))
#     draw = ImageDraw.Draw(image)
#
#     # 画圆
#     # 留出线宽的空间，确保圆不会被切掉
#     padding = line_width / 2
#     draw.ellipse([padding, padding, size_px - padding, size_px - padding],
#                  outline='#666666',
#                  width=int(line_width))
#
#     # 保存图片
#     image.save(f'circle_{scale}x.png')
#
#
# # 生成2x图片
# create_circle(2)
# # 生成3x图片
# create_circle(3)



