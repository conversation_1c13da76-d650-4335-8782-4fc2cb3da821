from PIL import Image
import os


def tint_image(image_path, color, save_path=None):
    """
    给图片着色
    :param image_path: 原图路径
    :param color: RGB颜色元组，例如(255, 0, 0)为红色
    :param save_path: 保存路径，默认为None
    :return: 着色后的图片对象
    """
    try:
        # 打开图片
        img = Image.open(image_path)
        # 确保图片有Alpha通道
        if img.mode != 'RGBA':
            img = img.convert('RGBA')

        # 获取图片数据
        data = img.getdata()

        # 处理每个像素
        new_data = []
        for item in data:
            # 保持原来的alpha值
            alpha = item[3]
            if alpha != 0:  # 如果不是完全透明
                # 使用新的颜色，但保持原来的alpha值
                new_data.append((*color, alpha))
            else:
                # 保持完全透明的像素
                new_data.append((0, 0, 0, 0))

        # 创建新图片
        new_img = Image.new('RGBA', img.size)
        new_img.putdata(new_data)

        # 如果指定了保存路径，则保存图片
        if save_path:
            new_img.save(save_path, 'PNG')

        return new_img
    except Exception as e:
        print(f"处理图片 {image_path} 时出错: {str(e)}")
        return None


def process_all_images(input_folder, output_folder, color):
    """
    处理文件夹中的所有图片
    :param input_folder: 输入文件夹路径
    :param output_folder: 输出文件夹路径
    :param color: RGB颜色元组
    """
    # 确保输出文件夹存在
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)

    # 获取所有文件
    for filename in os.listdir(input_folder):
        # 检查是否 为图片文件
        if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.gif')):
            input_path = os.path.join(input_folder, filename)
            # 保持原文件名，但强制使用.png后缀
            output_filename = os.path.splitext(filename)[0] + '.png'
            output_path = os.path.join(output_folder, output_filename)

            print(f"处理图片: {filename}")
            tint_image(input_path, color, output_path)


if __name__ == '__main__':
    # 设置文件夹路径
    input_folder = "原图片"
    output_folder = "改后图片"

    target_color = (216, 216, 216)

    # 处理所有图片
    process_all_images(input_folder, output_folder, target_color)
    print("处理完成！")
