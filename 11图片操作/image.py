from PIL import Image, ImageDraw, ImageColor
from enum import Enum
import os

class GradientDirection(Enum):
    LEFT_TO_RIGHT = 1
    TOP_TO_BOTTOM = 2
    TOP_LEFT_TO_BOTTOM_RIGHT = 3
    BOTTOM_LEFT_TO_TOP_RIGHT = 4

def generate_scaled_images(width, height, color):
    """
    Generate 2x and 3x scaled pure color images based on given dimensions and color.

    Parameters:
        width (int): The width of the original image.
        height (int): The height of the original image.
        color (str): The color of the image (e.g., "#RRGGBB" or "#RRGGBBAA").
    """
    folder_path = "图片"
    os.makedirs(folder_path, exist_ok=True)

    # 处理颜色格式
    if color.startswith('#'):
        if len(color) == 9:  # 包含透明度
            rgba = (int(color[1:3], 16), int(color[3:5], 16),
                   int(color[5:7], 16), int(color[7:9], 16))
        else:  # 不包含透明度
            rgb = ImageColor.getrgb(color)
            rgba = (*rgb, 255)
    else:  # rgba() 格式
        rgba = eval(color.replace('rgba', ''))

    color_cleaned = color.replace("#", "").replace("rgba(", "").replace(")", "")

    # 生成 2x 图片
    image_2x = Image.new("RGBA", (width * 2, height * 2), rgba)
    image_2x_name = f"{color_cleaned}_2x.png"
    image_2x_path = os.path.join(folder_path, image_2x_name)
    image_2x.save(image_2x_path, "PNG")
    print(f"2x image saved as {image_2x_path} with size {width * 2}x{height * 2}")

    # 生成 3x 图片
    image_3x = Image.new("RGBA", (width * 3, height * 3), rgba)
    image_3x_name = f"{color_cleaned}_3x.png"
    image_3x_path = os.path.join(folder_path, image_3x_name)
    image_3x.save(image_3x_path, "PNG")
    print(f"3x image saved as {image_3x_path} with size {width * 3}x{height * 3}")

def generate_gradient_image(colors, width, height, direction, scale):
    """
    Generate a scaled gradient color image with transparency support.

    Parameters:
        colors (list of str): A list of colors in hex format or rgba format.
        width (int): Width of the original image.
        height (int): Height of the original image.
        direction (GradientDirection): Gradient direction.
        scale (int): Scale factor (2 for 2x, 3 for 3x).
    """
    scaled_width = width * scale
    scaled_height = height * scale

    # 创建支持透明度的图片
    image = Image.new("RGBA", (scaled_width, scaled_height))
    draw = ImageDraw.Draw(image)

    # 解析颜色
    color_tuples = []
    for color in colors:
        if color.startswith('#'):
            if len(color) == 9:  # 8位十六进制（包含透明度）
                r = int(color[1:3], 16)
                g = int(color[3:5], 16)
                b = int(color[5:7], 16)
                a = int(color[7:9], 16)
                color_tuples.append((r, g, b, a))
            elif len(color) == 7:  # 6位十六进制（不包含透明度）
                r = int(color[1:3], 16)
                g = int(color[3:5], 16)
                b = int(color[5:7], 16)
                color_tuples.append((r, g, b, 255))
        else:
            rgba = eval(color.replace('rgba', ''))
            color_tuples.append(rgba)

    # 生成渐变
    if direction == GradientDirection.LEFT_TO_RIGHT:
        for x in range(scaled_width):
            ratio = x / scaled_width
            color = tuple(
                int(color_tuples[0][i] * (1 - ratio) + color_tuples[-1][i] * ratio)
                for i in range(4)
            )
            draw.line([(x, 0), (x, scaled_height)], fill=color)
    elif direction == GradientDirection.TOP_TO_BOTTOM:
        for y in range(scaled_height):
            ratio = y / scaled_height
            color = tuple(
                int(color_tuples[0][i] * (1 - ratio) + color_tuples[-1][i] * ratio)
                for i in range(4)
            )
            draw.line([(0, y), (scaled_width, y)], fill=color)
    elif direction == GradientDirection.TOP_LEFT_TO_BOTTOM_RIGHT:
        for i in range(max(scaled_width, scaled_height)):
            ratio = i / max(scaled_width, scaled_height)
            color = tuple(
                int(color_tuples[0][i] * (1 - ratio) + color_tuples[-1][i] * ratio)
                for i in range(4)
            )
            draw.line([(i, 0), (0, i)], fill=color)
    elif direction == GradientDirection.BOTTOM_LEFT_TO_TOP_RIGHT:
        for i in range(max(scaled_width, scaled_height)):
            ratio = i / max(scaled_width, scaled_height)
            color = tuple(
                int(color_tuples[0][i] * (1 - ratio) + color_tuples[-1][i] * ratio)
                for i in range(4)
            )
            draw.line([(0, scaled_height - i), (i, scaled_height)], fill=color)
    else:
        raise ValueError("Invalid direction. Use the GradientDirection enum.")

    return image

def save_gradient_images(colors, width, height, direction):
    """
    Save 2x and 3x gradient images with transparency support.

    Parameters:
        colors (list of str): A list of colors in hex format or rgba format.
        width (int): Width of the original image.
        height (int): Height of the original image.
        direction (GradientDirection): Gradient direction.
    """
    folder_path = "图片"
    os.makedirs(folder_path, exist_ok=True)

    # 处理文件名
    color_names = "_".join(c.replace("#", "").replace("rgba(", "").replace(")", "") for c in colors)

    # 生成和保存 2x 图片
    image_2x = generate_gradient_image(colors, width, height, direction, scale=2)
    image_2x_path = os.path.join(folder_path, f"{color_names}<EMAIL>")
    image_2x.save(image_2x_path, "PNG")
    print(f"2x image saved at {image_2x_path}")

    # 生成和保存 3x 图片
    image_3x = generate_gradient_image(colors, width, height, direction, scale=3)
    image_3x_path = os.path.join(folder_path, f"{color_names}<EMAIL>")
    image_3x.save(image_3x_path, "PNG")
    print(f"3x image saved at {image_3x_path}")

# 使用示例
# 1. 生成纯色图片（支持透明度）
generate_scaled_images(375, 230, "#232426FF")  # 完全不透明
# generate_scaled_images(375, 30, "rgba(35,36,38,128)")  # 半透明

# 2. 生成渐变图片（支持透明度）
# save_gradient_images(
#     colors=["#232426FF", "#2e2f3380"],  # 从不透明渐变到半透明
#     width=375,
#     height=110,
#     direction=GradientDirection.TOP_TO_BOTTOM
# )

# 使用 rgba() 格式
# save_gradient_images(
#     colors=["rgba(35,36,38,255)", "rgba(46,47,51,128)"],  # 从不透明渐变到半透明
#     width=375,
#     height=110,
#     direction=GradientDirection.TOP_TO_BOTTOM
# )
