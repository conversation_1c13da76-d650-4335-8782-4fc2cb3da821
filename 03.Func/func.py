# 函数
import random
import string
import os

def open_txt(file_name):
	# 获取脚本所在目录
	script_dir = os.path.dirname(os.path.abspath(__file__))
	# 构建完整的文件路径
	file_path = os.path.join(script_dir, file_name)
	with open(file_path, 'r', encoding = 'utf-8') as file:
		obj = file.read().split('\n')
	return obj

city_list = open_txt('city.txt')
project_list = open_txt('project.txt')
money_list = open_txt('money.txt')

result = []
for c in city_list:
	for p in project_list:
		for m in money_list:
			result.append(c + p + m)

with open('keyword.txt', 'wb') as file:
	file.write('\n'.join(result).encode('utf-8'))