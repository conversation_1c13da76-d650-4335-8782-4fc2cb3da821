import math
import queue
import time

from auth import get_tokens
from contact import getContacts, importContact
from order import order, getTripTime
from utils import get_config, split_array, get_weekday

import concurrent.futures

target_time = str(get_config('target-time'))

taskOrderQueue = queue.Queue()


def getStockId(token: str, date: str):
    ticketInfo = get_config('ticket-type')
    return getTripTime(token, date, list(ticketInfo))


def orderCompatible(token: str, contacts: list, tripTime):
    resultCode = order(token, contacts, tripTime)
    if resultCode is None:
        print('可能是网络异常导致下单失败')
        taskOrderQueue.put(("orderCompatible", token, contacts, tripTime))
    elif resultCode == '-1':
        print('服务器提示操作过于频繁，我们将把这笔订单放到队列里面去等下次再发起下单')
        taskOrderQueue.put(("orderCompatible", token, contacts, tripTime))


# 处理失败队列里面的任务
def checkQueue():
    print(f"队列里面还有{taskOrderQueue.qsize()}个未执行完的任务")
    if not taskOrderQueue.empty():
        try:
            task_info = taskOrderQueue.get(timeout=1)  # 设置timeout以避免线程被阻塞
            task_name, token, contacts, tripTime = task_info
            if task_name == "orderCompatible":
                time.sleep(15)
                orderCompatible(token, contacts, tripTime)
            else:
                print(f"Unknown queue task: {task_name}")

            checkQueue()
        except queue.Empty:
            return  # 队列为空，退出循环


def work(token: str, tripTime: list):
    contacts = getContacts(token)
    split_contacts_arrays = list(split_array(contacts, math.ceil(len(contacts) / len(tripTime))))
    for i, contactsArrays in enumerate(split_contacts_arrays):
        contactsArray = list(contactsArrays)

        # 把联系人分成4个4个一组去下单
        if len(contactsArray) > 5:
            split_arrays = list(split_array(contactsArray, 5))
            for _, chunk in enumerate(split_arrays):
                contactsData = list(chunk)
                orderCompatible(token, contactsData, tripTime[i])
                time.sleep(15)

        else:
            orderCompatible(token, contactsArray, tripTime[i])
            time.sleep(15)


def run():
    my_futures = []
    with concurrent.futures.ThreadPoolExecutor() as executor:
        tokens = get_tokens()

        timePeriod = int(get_config('time-period'))
        tripTimeArray = getStockId(tokens[0], target_time)
        tokenTripTimeMap = {}

        for ticketType in tripTimeArray:
            for index, tripTime in enumerate(ticketType[:int(timePeriod)]):
                tokensIndex = index % len(tokens)
                token = tokens[tokensIndex]
                tokenTripTimeItem = tokenTripTimeMap.get(token)
                if tokenTripTimeItem is None:
                    tokenTripTimeItem = []
                tokenTripTimeItem.append(tripTime)
                tokenTripTimeMap[token] = tokenTripTimeItem

        for token in tokenTripTimeMap.keys():
            my_futures.append(executor.submit(work, token, tokenTripTimeMap[token]))

    concurrent.futures.wait(my_futures)

    checkQueue()

    time.sleep(20)


def waitRun():
    startTime = get_config('start-time')

    print(f"服务已启动, 将在{str(startTime)}执行抢{target_time}({get_weekday(target_time)})的票")
    start_timestamp = int(start_time.timestamp() * 1000)
    while True:
        nowTimestamp = int(time.time() * 1000)
        if start_timestamp <= nowTimestamp:
            run()
            break
        else:
            time.sleep(0.5)


if __name__ == '__main__':
    start_time = get_config('start-time')

    importContact(get_tokens())

    waitRun()
