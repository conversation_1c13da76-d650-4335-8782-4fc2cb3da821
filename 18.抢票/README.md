## 梵净山

### 限制

- 实名预约刷身份证进场；
- 一笔订单最多同时约4张身份证；
- 提交订单不能太频繁否则会提交失败；

### 预约时间
- 预约4天后的票（比如9月22约9月25号的票）
- 早上7点0分开始预约 晚上10点30停止预约

### 门票类型&售价
- 江口东门
	```
	【江口东门】亚、残运会5折门票+观光车往返+意外险（杭州、宁波、温州、湖州、绍兴、金华） {'prodId': 'P2023090400000010801', 'recordId': '4241', 'price': '80.00'}
	
	【江口东门】成人门票+观光车往返+意外险（成人18-59岁） {'prodId': 'P2022012600000010302', 'recordId': '857', 'price': '130.00'}
	
	【江口东门】老人免门票+观光车往返+意外险（60周岁以上老人） {'prodId': 'P2022042300000010802', 'recordId': '1258', 'price': '30.00'}
	
	【江口东门】儿童免门票+观光车往返+意外险（1.4米以上14周岁以下儿童） {'prodId': 'P2022042300000012001', 'recordId': '1259', 'price': '30.00'}
	
	【江口东门】门票5折+观光车往返+意外险（14-17岁未成年） {'prodId': 'P2022042300000012803', 'recordId': '1260', 'price': '80.00'}
	
	【江口东门】门票+观光车往返+意外险（港澳台及境外人员） {'prodId': 'P2022032300000017001', 'recordId': '1027', 'price': '130.00'}
	
	【江口东门】“东莞居民”免门票+观光车往返+意外险 {'prodId': 'P2023041900000032503', 'recordId': '2835', 'price': '30.00'}
	
	【江口东门】“广东居民门票5折（不含东莞）”+观光车往返+意外险 {'prodId': 'P2023041900000033601', 'recordId': '2837', 'price': '80.00'}
	```
- 印江西门
	```
	【印江西门】亚、残运会5折门票+观光车往返+意外险（杭州、宁波、温州、湖州、绍兴、金华） {'prodId': 'P2023090400000008201', 'recordId': '4240', 'price': '80.00'}
	
	【印江西门】成人门票+观光车往返+意外险（成人18-59岁） {'prodId': 'P2022112700000008801', 'recordId': '2115', 'price': '130.00'}
	
	【印江西门】老人免门票+观光车往返+意外险（60周岁以上） {'prodId': 'P2022042300000020301', 'recordId': '1262', 'price': '30.00'}
	
	【印江西门】儿童免门票+观光车往返+保险（1.4米以上14周岁以下儿童） {'prodId': 'P2022042300000019801', 'recordId': '1264', 'price': '30.00'}
	
	【印江西门】5折门票+观光车往返+意外险（14-17周岁未成年） {'prodId': 'P2022042300000014201', 'recordId': '1265', 'price': '80.00'}
	
	【印江西门】门票+观光车往返+意外险（港澳台及境外人员） {'prodId': 'P2023050800000011602', 'recordId': '3147', 'price': '130.00'}
	
	【印江西门】“东莞居民”免门票+观光车往返+意外险 {'prodId': 'P2023041900000033502', 'recordId': '2836', 'price': '30.00'}
	
	【印江西门】“广东居民门票5折（不含东莞）”+观光车往返+意外险 {'prodId': 'P2023041900000032701', 'recordId': '2838', 'price': '80.00'}
	```
### 场次
- 江口东门
	```
	
	```
- 印江西门
	```
	```

### 评价
- 江口东门的门票好卖些

### 需要修改的配置
#### 抢票流程
- user.xlsx填入身份证信息
- accounts.json里填入账号token
- tokens.txt里填入account.json里面的id，表示用哪几个账号参与这次抢票
- config.yaml里面修改抢票开始时间、抢的票时间和票类型
- 运行main.py
- 微信上支付
#### 退换票
- transfer.txt里填入一个id就行了
- 运行transfer.py，手动输入信息：票类型一般为{"prodId": "P2022012600000010302", "recordId": "857", "price": "130.00"}，索引场次是从0开始
#### 查询订单信息
- 运行order.py
