import json
import os
import time

from contact import getContacts, importContact, deleteContacts, addContact
from order import order, printTicketType, getTripTime
from utils import getTokensByIds


def readTransferTokenIds():
    tokens = []
    if os.path.exists('transfer.txt'):
        with open('transfer.txt', 'r', encoding='utf-8') as file:
            for line in file:
                line = line.strip()

                if line:
                    tokens.append(line)

    return tokens


def readTransferToken():
    return getTokensByIds(readTransferTokenIds())


def transfer(targetDate, ticketType, name, cardId, timeIndex):
    token = readTransferToken()[0]
    deleteContacts([token])
    addContact(token, {"name": name, "cid": cardId})

    tripTime = getTripTime(token, targetDate, [json.loads(ticketType)])[0][int(timeIndex)]

    contacts = getContacts(token)

    print(f"联系人信息：{contacts}")

    while True:
        result = order(token, [contacts[0]], tripTime)
        print(f"下单结果{result}")
        if result is None:
            print('可能是网络异常导致下单失败')
        elif len(result) == 0:
            break
        elif result == '-1':
            print('服务器提示操作过于频繁，我们将把这笔订单放到队列里面去等下次再发起下单')
            time.sleep(10)
        elif result == '1004':
            # 90天内购买次数已达上限，无法继续购买,限购数量：1
            break
        else:
            time.sleep(2)


if __name__ == '__main__':
    indexType = input("请输入下面你要执行任务的代号\n1：查询票类型\n2：转移门票\n").strip().lower()
    if indexType == '1':
        printTicketType(readTransferToken()[0])
    elif indexType == '2':
        inputDate = input("请输入抢票日期:").strip()
        inputTicketType = input("请输入票类型:").strip()
        inputName = input("请输入姓名:").strip()
        inputCardId = input("请输身份证号:").strip()
        inputTimeIndex = input("请输入场次索引:").strip()
        transfer(inputDate, inputTicketType, inputName, inputCardId, inputTimeIndex)
