import json
import math

import requests

from order import getTripTime
from utils import get_header, readContacts, split_array, get_config


def deleteContact(token: str, recordId: str):
    data = {"resortId": 0, "recordId": recordId, "corpId": "2000001053"}
    response = requests.post("https://etrip.icbc.com.cn/api-trip/generalContact/delete?corpId=2000001053",
                             headers=get_header(token), json=data, verify=False)
    if response is not None and response.status_code == 200:
        content = json.loads(response.content)
        return int(content["code"]) == 0
    return False


def addContact(token: str, contact):
    data = {"resortId": 0, "idType": "0", "name": contact['name'], "idNo": contact['cid'], "corpId": "2000001053"}
    response = requests.post("https://etrip.icbc.com.cn/api-trip/generalContact/add?corpId=2000001053",
                             headers=get_header(token), json=data, verify=False)
    if response is not None and response.status_code == 200:
        content = json.loads(response.content)
        return int(content["code"]) == 0
    return False


def getPhone(token: str):
    response = requests.get("https://etrip.icbc.com.cn/api-trip/trip/info/selectSrcUnionInfo?srcType=4",
                            headers=get_header(token), verify=False)
    if response is not None and response.status_code == 200:
        content = json.loads(response.content)
        if int(content["code"]) == 0:
            return content['data']['phone']
    return False


def getContacts(token: str):
    response = requests.get("https://etrip.icbc.com.cn/api-trip/generalContact?resortId=0&corpId=2000001053",
                            headers=get_header(token), verify=False)
    phone = getPhone(token)
    result = []
    if response is not None and response.status_code == 200:
        content = json.loads(response.content)
        if int(content["code"]) == 0:
            contacts = content['data']['data']
            for contact in contacts:
                contact['phone'] = phone
                result.append(contact)

    return result


def deleteContacts(tokens: list):
    for token in tokens:
        contacts = getContacts(token)
        for contact in contacts:
            deleteContact(token, contact['recordId'])


def getStockId(token: str, date: str):
    ticketInfo = get_config('ticket-type')
    return getTripTime(token, date, list(ticketInfo))


def importContact(tokens: list):
    deleteContacts(tokens)

    timePeriod = int(get_config('time-period'))
    tripTimeArray = getStockId(tokens[0], str(get_config('target-time')))
    tokenTripTimeMap = {}

    for ticketType in tripTimeArray:
        for index, tripTime in enumerate(ticketType[:int(timePeriod)]):
            tokensIndex = index % len(tokens)
            token = tokens[tokensIndex]
            tokenTripTimeItem = tokenTripTimeMap.get(token)
            if tokenTripTimeItem is None:
                tokenTripTimeItem = []
            tokenTripTimeItem.append(tripTime)
            tokenTripTimeMap[token] = tokenTripTimeItem

    tokenPiece = []
    for token in tokenTripTimeMap.keys():
        for _ in range(len(tokenTripTimeMap[token])):
            tokenPiece.append(token)

    allContacts = readContacts()
    split_arrays = split_array(allContacts, math.ceil(len(allContacts) / len(tokenPiece)))
    for i, chunk in enumerate(split_arrays):
        contacts = list(chunk)

        for contact in contacts:
            addContact(tokenPiece[i], contact)

    for token in tokens:
        print(f"添加完成,系统里面有{len(getContacts(token))}位联系人")
