import json

from auth import get_tokens
from utils import get_header, getBuyerName, send_notify, saveTicket, getTokenTagByToken, get_config
import requests
import urllib3

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

proxy_host = 'b815.kdltps.com'
proxy_port = '15818'
proxy_username = 't19578262643387'
proxy_password = 'bt57mcxa'

proxy = f'http://{proxy_username}:{proxy_password}@{proxy_host}:{proxy_port}'
proxies = {
    'http': proxy,
    'https': proxy
}


def onlineOrderSubmit(token: str, contacts: list, prodId: str,
                      date: str, useTimeBeg: str, useTimeEnd: str, timeRangeId: str,
                      price: str):
    try:
        corpId = '2000001053'
        sumPrice = "{:.2f}".format(float(price) * len(contacts))
        data = {
            "corpId": corpId,
            "totalAmount": sumPrice,
            "salesChannel": "1",
            "payWay": "4",
            "placeOrderWay": 0,
            "buyerInfo": {
                "name": getBuyerName(),
                "mobile": contacts[0]['phone']
            },
            "ticketInfos": [
                {
                    "prodId": prodId,
                    "travelDate": date,
                    "ticketNum": len(contacts),
                    "ticketType": "1",
                    "ticketUsers": contacts,
                    "timeRangeId": timeRangeId,
                    "useTimeBeg": useTimeBeg,
                    "useTimeEnd": useTimeEnd,
                    "subTickets": []
                }
            ],
            "orderCouponNo": "",
            "customerRemark": "",
            "showSessionInfo": {}
        }
        response = requests.post("https://etrip.icbc.com.cn/api-trip/productTicketPurchase/onlineOrderSubmit"
                                 f"/{corpId}?corpId={corpId}",
                                 headers=get_header(token), json=data, verify=False, timeout=5, proxies=proxies)
    except Exception as e:
        print("onlineOrderSubmit 请求发生异常:", e)
        response = None

    if response is not None and response.status_code == 200:
        content = json.loads(response.content)
        if int(content["code"]) == 0:
            return content["data"]
    return None


def queryDayTime(token: str, prodId: str, recordId: str, date: str):
    corpId = '2000001053'
    data = {
        "corpId": corpId,
        "prodId": prodId,
        "recordId": recordId,
        "buyDate": date,
        "ticketCategory": "1",
        "salesChannel": "1",
        "remainTicket": "1"
    }
    response = requests.post(
        f"https://etrip.icbc.com.cn/api-trip/trip/productManage/queryDayTimeShareQuote/mw/{corpId}?corpId={corpId}",
        headers=get_header(token), json=data, verify=False)
    result = []
    if response is not None and response.status_code == 200:
        content = json.loads(response.content)
        if int(content["code"]) == 0:
            result = content['data']['data']['dayTimeLimitRuleCoList']

    return result


def queryWayArray(token: str):
    corpId = '2000001053'
    response = requests.get(
        f"https://etrip.icbc.com.cn/api-trip/trip/onlineTicketing/queryAllResortDetailList/{corpId}?corpId={corpId}"
        f"&resortId=100&ticketTypeCategoryFlag=1",
        headers=get_header(token), verify=False)
    result = []
    if response is not None and response.status_code == 200:
        content = json.loads(response.content)
        if int(content["code"]) == 0:
            productType = content['data']['data']['prodCo'][0]['ticketTypeProduct']
            result.append(productType[0]['ticket'])
            result.append(productType[1]['ticket'])

    return result


def getTripTime(token: str, date: str, ticketInfo: list):
    result = []
    for info in ticketInfo:
        dayTimes = queryDayTime(token, info['prodId'], info['recordId'], date)
        item = []
        for daytime in dayTimes:
            item.append({
                "prodId": info['prodId'],
                "recordId": info['recordId'],
                "price": info['price'],
                "beginTime": daytime['beginTime'],
                "endTime": daytime['endTime'],
                "timeRangeId": daytime['timeRangeId'],
                "date": date
            })
        result.append(item)
    return result


def order(token: str, contacts: list, tripTime):
    result = onlineOrderSubmit(token, contacts, tripTime['prodId'], tripTime['date'],
                               tripTime['beginTime'], tripTime['endTime'], tripTime['timeRangeId'],
                               tripTime['price'])
    if result is not None:
        code = str(result.get('errCode'))
        if code is not None and len(code) == 0 and result.get("data") is not None:
            send_notify("梵净山下单成功", f"请抓紧时间支付订单 游玩时间信息：{tripTime}")
        else:
            print(f"下单失败 服务器返回结果:{result}")
            send_notify("梵净山下单失败", f"下单失败 服务器返回结果:{result}")
        return code
    return None


def getOrderList(token: str):
    response = requests.get(
        f"https://etrip.icbc.com.cn/api-trip/orderinfo/appmyorderlist?corpId=2000001053&myOrderStatus=0&salesChannels=1%7C6&pageNum=1&pageSize=50",
        headers=get_header(token), verify=False)
    result = []
    if response is not None and response.status_code == 200:
        content = json.loads(response.content)
        if int(content["code"]) == 0:
            result = content['data']['data']

    return result


def makeTicket(date):
    result = {}
    for token in get_tokens():
        orderList = getOrderList(token)
        for orderItem in orderList:
            orderStatus = int(orderItem['orderStatus'])
            if orderStatus == 1 or orderStatus == 4:
                ticketInfoList = orderItem['ticketInfoList']
                if ticketInfoList[0]['travelDate'] == date:
                    line = ticketInfoList[0]['ticketName'][1:5]
                    time = f"{ticketInfoList[0]['useTimeBeg']}-{ticketInfoList[0]['useTimeEnd']}".replace(':', '：')
                    lineData = {}
                    if result.get(line) is not None:
                        lineData = result[line]

                    timeData = []
                    if lineData.get(time) is not None:
                        timeData = lineData[time]

                    for ticketInfo in ticketInfoList:
                        ticketInfo['accountTag'] = getTokenTagByToken(token)
                        timeData.append(ticketInfo)

                    lineData[time] = timeData
                    result[line] = lineData

    saveTicket(result, date)


def printTicketType(token: str):
    ticketType = queryWayArray(token)

    for ticket in ticketType[0]:
        ticketInfo = {'prodId': ticket['prodId'], 'recordId': ticket['recordId'], 'price': ticket['lowPrice']}

        print(f"{ticket['proName']} {json.dumps(ticketInfo)}")

    print("")

    for ticket in ticketType[1]:
        ticketInfo = {'prodId': ticket['prodId'], 'recordId': ticket['recordId'], 'price': ticket['lowPrice']}
        print(f"{ticket['proName']} {json.dumps(ticketInfo)}")


if __name__ == '__main__':
    cmd_type = input("请输入下面你要执行任务的代号\n1：查询票类型\n2：生成票信息\n").strip().lower()

    if cmd_type == "1":
        printTicketType(get_tokens()[0])

    elif cmd_type == "2":
        inputDate = input("请输入要生成订单的日期:").strip()
        if len(inputDate) == 0:
            inputDate = str(get_config('target-time'))
            print(f"默认日期：{inputDate}")
        makeTicket(inputDate)
