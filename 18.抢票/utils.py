import json
import os
import random
import smtplib
import threading
from datetime import datetime
from email.header import Header
from email.mime.multipart import MI<PERSON><PERSON><PERSON>ip<PERSON>
from email.mime.text import MIMEText

import yaml
import openpyxl


def get_header(token: str):
    headers = {
        "Connection": "keep-alive",
        "Accept": "application/json",
        "Authorization": "",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x6309021a) XWEB/8391 Flue",
        "Content-Type": "application/json; charset=utf-8",
        "Origin": "https://etrip.icbc.com.cn",
        "Sec-Fetch-Site": "same-origin",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Dest": "empty",
        "Accept-Encoding": "gzip, deflate, br",
        "Accept-Language": "zh-CN,zh",
        "Cookie": f"bcssToken={token}; X-Tag=gray-trip"
    }
    return headers


def read_config():
    with open('config.yaml', 'r', encoding='utf-8') as file:
        data = yaml.safe_load(file)
    return data


def get_config(key):
    return read_config()[key]


def split_array(array, chunk_size):
    for i in range(0, len(array), chunk_size):
        yield array[i:i + chunk_size]


def get_weekday(date_string):
    try:
        # 将日期字符串解析为 datetime 对象
        date_obj = datetime.strptime(date_string, '%Y-%m-%d')
        # 获取星期几，星期一为 0，星期日为 6
        weekday = date_obj.weekday()
        # 星期名称列表
        weekdays = ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日']
        # 返回对应的星期名称
        return weekdays[weekday]
    except ValueError:
        return "无效的日期格式，请输入正确的日期格式（例如：YYYY-MM-DD）"


def readContacts(pathname='user'):
    workbook = openpyxl.load_workbook(f'user/{pathname}.xlsx')
    sheet = workbook.active
    result = []
    for row in sheet.iter_rows(min_row=1, values_only=True):
        # result.append(({'name': str(row[0]).strip(), 'cid': str(row[1]).strip(), 'phone': str(row[3])}))
        result.append(({'name': str(row[0]).strip(), 'cid': str(row[1]).strip(), 'phone': '***********'}))
    return result


def getBuyerName():
    with open('assets/names.json', 'r', encoding='utf-8') as file:
        return random.choice(json.loads(file.read()))


def getAccounts():
    with open('assets/accounts.json', 'r', encoding='utf-8') as file:
        return json.loads(file.read())


def readAccountsDic():
    result = {}
    accounts = getAccounts()
    for account in accounts:
        result[account['id']] = account

    return result


def getTokensByIds(ids: list):
    tokens = []
    accountsDic = readAccountsDic()
    for item in ids:
        account = accountsDic.get(item)
        if account is not None:
            tokens.append(account['token'])

    return tokens


def getTokenTagByToken(token: str):
    for account in getAccounts():
        if account['token'] == token:
            return account['tag']

    return None


def send_email(sender_email: str, sender_password: str, receiver_emails, subject, body):
    message = MIMEMultipart()
    message['From'] = str(sender_email)
    message['To'] = ", ".join(receiver_emails) if isinstance(receiver_emails, list) else str(receiver_emails)
    message['Subject'] = Header(subject, 'utf-8')

    # 添加正文内容
    message.attach(MIMEText(body, 'plain', 'utf-8'))

    # SMTP服务器设置（根据你的邮箱提供商设置相应的SMTP服务器和端口）
    smtp_server = 'smtp.126.com'

    try:
        smtpObj = smtplib.SMTP()
        smtpObj.connect(smtp_server)
        smtpObj.login(sender_email, sender_password)
        smtpObj.sendmail(sender_email, receiver_emails, message.as_string())
        smtpObj.quit()
    except Exception as e:
        print("邮件发送失败:", str(e))


def send_notify(subject, body):
    config = read_config()
    thread = threading.Thread(target=send_email, args=(
        config['notify-sender-email'], config['notify-sender-password'], config['notify-receiver-email'], subject,
        body))
    thread.start()


def saveTicket(ticket, date):
    if not os.path.exists(f"output/{date}"):
        os.makedirs(f"output/{date}")

    for line in ticket.keys():
        if not os.path.exists(f"output/{date}/{line}"):
            os.makedirs(f"output/{date}/{line}")

        lineData = ticket[line]
        for time in lineData.keys():
            if not os.path.exists(f"output/{date}/{line}/{time}"):
                os.makedirs(f"output/{date}/{line}/{time}")

            timeData = lineData[time]

            accounts = []
            for timeDataItem in timeData:
                accounts.append(timeDataItem['accountTag'])

            with open(f"output/{date}/{line}/{time}/data.txt", 'a', encoding='utf-8') as file:
                file.write(f"数量：{len(timeData)}\n涉及账号：{set(accounts)}\n{timeData}")
