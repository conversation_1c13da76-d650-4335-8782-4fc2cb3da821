# https://sp1.baidu.com/8aQDcjqpAAV3otqbppnN2DJv/api.php?resource_id=28286&from_mid=1&&format=json&ie=utf-8&oe=utf-8&query=%E7%94%B5%E5%BD%B1%E6%8E%92%E8%A1%8C%E6%A6%9C&sort_key=16&sort_type=1&stat0=&stat1=&stat2=&stat3=&pn=0&rn=8&cb=jQuery11020044685299534064415_1668392722581&_=1668392722605
# https://sp1.baidu.com/8aQDcjqpAAV3otqbppnN2DJv/api.php?resource_id=28286&from_mid=1&&format=json&ie=utf-8&oe=utf-8&query=%E7%94%B5%E5%BD%B1%E6%8E%92%E8%A1%8C%E6%A6%9C&sort_key=16&sort_type=1&stat0=&stat1=&stat2=&stat3=&pn=8&rn=8&cb=jQuery11020044685299534064415_1668392722581&_=1668392722604
import json
import random
import re
import time

from splinter.browser import Browser

result = []
browser = Browser(driver_name='chrome')
for i in range(0, 8):
    api_url = 'https://sp1.baidu.com/8aQDcjqpAAV3otqbppnN2DJv/api.php?resource_id=28286&from_mid=1&&format=json&ie=utf-8&oe=utf-8&query=%E7%94%B5%E5%BD%B1%E6%8E%92%E8%A1%8C%E6%A6%9C&sort_key=16&sort_type=1&stat0=&stat1=&stat2=&stat3=&pn=' + str(
        i * 8) + '&cb=jQuery11020044685299534064415_1668392722581&_=1668392722604'
    try:
        browser.visit(api_url)
    except Exception as e:
        print(str(e))
        continue
    re_str = 'jQuery11020044685299534064415_1668392722581\((.*?)\)'
    try:
        rel_obj = json.loads(re.findall(re_str, browser.html, re.S)[0])  #re.S表示可以匹配换行符
        data_list = rel_obj['data'][0]['result']
    except Exception as e:
        print(str(e))
        continue
    for data in data_list:
        result.append('%s\t%s' % (data['ename'], data['additional'].replace('豆瓣:', '')))
    time.sleep(random.uniform(1.0, 3.0))

browser.quit()
