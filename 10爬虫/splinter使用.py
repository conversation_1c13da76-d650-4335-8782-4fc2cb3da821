from splinter.browser import Browser
from lxml import etree
import json
import re

# <a class="title" target="_blank" href="/p/5ab9319320aa">SpringBoot开发的实用小工具集，YYDS</a>
# <a class="title" target="_blank" href="/p/e8ef97201360">来来去去情感篇 / 世间不过人情冷暖和兵荒马乱</a>

browser = Browser(driver_name='chrome')

for i in range(1, 3):
    browser.visit('http://ent.qianlong.com/%s.shtml' % i)
    # re_str = '<span class="s_pc_zfenx" data-id="http\://ent\.qianlong\.com/\d+/\d+/\d+\.shtml" data-title="(.*?)">'
    # for title in re.findall(re_str,browser.html):
    # 	print(title)
    html = etree.HTML(browser.html)

    # 标题
    # /html/body/div[1]/div[2]/div/div[1]/div[3]/div[2]/div[1]/a
    # /html/body/div[1]/div[2]/div/div[1]/div[3]/div[2]/div[1]/a
    # /html/body/div[1]/div[2]/div/div[1]/div[3]/div[4]/div[2]/div/h3/a
    # 时间
    # /html/body/div[1]/div[2]/div/div[1]/div[3]/div[1]/div[3]/div/span
    # /html/body/div[1]/div[2]/div/div[1]/div[3]/div[2]/div[3]/div/span
    # /html/body/div[1]/div[2]/div/div[1]/div[3]/div[4]/div[2]/div/div/div/span

    for div in html.xpath('/html/body/div[1]/div[2]/div/div[1]/div[3]/div'):
        try:
            title = div.xpath('div[1]/a/text()')[0]
            date = div.xpath('div[3]/div/span/text()')[0]
        except:
            title = div.xpath('div[2]/div/h3/a/text()')[0]
            date = div.xpath('div[2]/div/div/div/span/text()')[0]
        print(title.replace('  ', '').replace('\n', ''), date)

    # for a in html.xpath('/html/body/div[1]/div[2]/div/div[1]/div[3]/div/div[1]/a'):
    #     print(a.xpath('@href')[0], a.xpath('text()')[0])

browser.quit()

# browser.visit('https://www.jianshu.com')  # 访问指定页面
# # re_str = '<a class="title" target="_blank" href="/p/[A-Za-z0-9]+">(.*?)</a>'
# print(browser.html)
# #
# # for title in re.findall(re_str, browser.html):
# #     print(title)
# html = etree.HTML(browser.html)
# for li in html.xpath('/html/body/div[1]/div/div[1]/div[3]/ul/li'):
#     priceNum = li.xpath('div/div/span[1]/text()')[0]
#     authorName = li.xpath('div/div/a[1]/text()')[0]
#     commentsNum = li.xpath('div/div/a[2]/text()')[0]
#     likeNum = li.xpath('div/div/span[1]/text()')[0]
#     print(priceNum, authorName, commentsNum, likeNum)


# /html/body/div[1]/div/div[1]/div[3]/ul/li[1]/div/div/a[1]
# /html/body/div[1]/div/div[1]/div[3]/ul/li[2]/div/div/a[1]
# /html/body/div[1]/div/div[1]/div[3]/ul/li[1]/div/div/span[1]
# /html/body/div[1]/div/div[1]/div[3]/ul/li[1]/div/div/span[2]
#
# /html/body/div[1]/div/div[1]/div[3]/ul/li[1]/div/div/a[2]
# /html/body/div[1]/div/div[1]/div[3]/ul/li[2]/div/div/a[2]

# browser.html # 取源代码
# browser.title # 取网页标题
#
# browser.find_by_id('test')[0].fill('test') # 通过元素id寻找目标组件并写入
# browser.find_by_css('test')[0].fill('test') # 通过元素css寻找目标组件并写入
# browser.find_by_text('test')[0].click() # 通过元素标题寻找目标组件并点击
# browser.find_by_value('test')[0].click() # 通过元素值寻找目标组件并点击
