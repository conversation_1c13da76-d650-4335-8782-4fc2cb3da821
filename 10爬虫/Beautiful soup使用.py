# html = """
# <html><head><title>The Do<PERSON>ouse's story</title></head>
# <body>
# <p class-"title" name="dromouse"><b>The Dormouse's story</b></p>
# <p class-"story">Once upon a time there were three little sisters; and their names were
# <a href = "http://example.com/elsie" class="sister" id="link1"><!-- Elsie --></a>,
# <a href = "http://example.com/lacie" class="sister" id="link2"><PERSON><PERSON></a> and
# <a href = "http://example.com/tillie" class="sister" id="link3"><PERSON><PERSON> </a>;
# and they lived at the bottom of a well.</p>
# <p class-"story"> ...</p>
# """
# from bs4 import BeautifulSoup
# soup = BeautifulSoup(html, 'lxml')
# print (soup.title)
# print(type(soup.title))
# print(soup.title.string)
# print (soup.head)
# print(soup.p)

html ="""
<html>
    <body>
        <p class="story">
            Once upon a time there were three little sisters;and their names were 
            <a href="http://example.com/elsie" class="sister" id="link1">Bob</a><a href="http://example.com/lacie" class="sister" id="link2">Lacie</a>
        </p>
"""
from bs4 import BeautifulSoup
soup = BeautifulSoup(html, 'lxml')
# print ('Next Sibling:')
# print(type(soup.a.next_sibling))
# print(soup.a.next_sibling)
# print(soup.a.next_sibling.string)
# print('Parent:')
# print(type(soup.a.parents))
# print(list(soup.a.parents)[0])
# print(list(soup.a.parents)[0].attrs['class'])
print((soup.a.parent))
