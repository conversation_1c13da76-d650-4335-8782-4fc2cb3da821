import time
import json
from selenium.webdriver.common.by import By
from selenium import webdriver
from selenium.webdriver import ChromeOptions

driver = webdriver.Chrome()
# driver.get("https://tieba.baidu.com/")


#<input id="wd1" class="search_ipt search_inp_border j_search_input tb_header_search_input" name="kw1" value="" autocomplete="off" size="42"
# tabindex="1" maxlength="100" x-webkit-grammar="builtin:search" x-webkit-speech="true" type="text">

# tb_header = driver.find_element(By.XPATH, "//*[@id='tb_header_search_form']")
# tb_header.find_element(By.XPATH, "//*[@id='wd1']").send_keys("python")
# driver.find_element(By.XPATH, ".//*[@id='wd1']").send_keys("python")  # .//和//都可以

# driver.find_element(By.ID, 'wd1').send_keys('python')
# driver.find_element(By.NAME, 'kw1').send_keys('python')
# driver.find_element(By.CLASS_NAME, 'tb_header_search_input').send_keys('python')
# driver.find_element(By.TAG_NAME, 'input').send_keys('python')
# driver.find_element(By.CSS_SELECTOR, '#wd1').send_keys('python')
# driver.find_element(By.PARTIAL_LINK_TEXT, '地').click()
# driver.find_element(By.CSS_SELECTOR, "input[id='wd1']").send_keys('python') 出错

driver.get("http://news.baidu.com/")
element1 = driver.find_element(By.CSS_SELECTOR,"label[class='not-checked']")

element2 = driver.find_element(By.CSS_SELECTOR,"#newstitle")

print(element1.text)
time.sleep(60)
# driver.quit()
# wd = webdriver.Chrome()
# wd.get("https://www.baidu.com")    # 打开百度浏览器
# wd.find_element(By.XPATH, '//*[@id="kw"]').send_keys('selenimu')
# wd.find_element(By.XPATH, '//*[@id="su"]').click()
# # 老方法不可用
# # wd.find_element_by_id("kw").send_keys("selenium")   # 定位输入框并输入关键字
# # wd.find_element_by_id("su").click()   #点击[百度一下]搜索
# wd.quit()   #关闭浏览器


