# import pandas as pd
#
# # 读取CSV文件，指定编码格式
# csv_file = '大决策APP-由我解决Bug.csv'  # 替换为你的CSV文件路径
# try:
#     df = pd.read_csv(csv_file, encoding='utf-8')  # 先尝试UTF-8
# except UnicodeDecodeError:
#     try:
#         df = pd.read_csv(csv_file, encoding='gbk')  # 如果UTF-8失败，尝试GBK
#     except UnicodeDecodeError:
#         df = pd.read_csv(csv_file, encoding='ISO-8859-1')  # 如果GBK失败，尝试ISO-8859-1
#
# # 将数据写入Excel文件
# excel_file = '大决策APP-由我解决Bug.xlsx'  # 替换为你想保存的Excel文件路径
# df.to_excel(excel_file, index=False)  # index=False表示不写入行索引
#
# print(f"CSV文件 '{csv_file}' 已成功转换为Excel文件 '{excel_file}'")



# import pandas as pd
#
# # 读取Excel文件
# file_path = '大决策APP-由我解决Bug.xlsx'
# df = pd.read_excel(file_path, sheet_name='Sheet1')
#
# # 选择需要的列
# columns_to_keep = ['Bug编号', '所属模块', 'Bug标题', '创建日期', '解决日期']
# filtered_df = df[columns_to_keep]
#
# # 保存到新的Excel文件
# output_file_path = '过滤版_大决策APP-由我解决Bug.xlsx'
# filtered_df.to_excel(output_file_path, index=False)
#
# print(f"过滤后的文件已保存到: {output_file_path}")


import pandas as pd

# 读取Excel文件
df = pd.read_excel('大决策APP-由我解决Bug.xlsx')

# 将DataFrame转换为JSON
json_data = df.to_json(orient='records', force_ascii=False)

# 将JSON数据写入文件
with open('output.json', 'w', encoding='utf-8') as f:
    f.write(json_data)

print("Excel文件已成功转换为JSON格式并保存为output.json")