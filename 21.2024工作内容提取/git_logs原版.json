[{"author": "zeng<PERSON>n", "date": "2025-01-20 16:43:37 +0800", "message": "更新pod"}, {"author": "zeng<PERSON>n", "date": "2025-01-20 16:41:28 +0800", "message": "。。"}, {"author": "zeng<PERSON>n", "date": "2025-01-20 16:31:11 +0800", "message": "feat: 修改个人中心，动态配置我的保险 fix: 直播支付闪退"}, {"author": "zeng<PERSON>n", "date": "2025-01-20 14:11:20 +0800", "message": "。。。"}, {"author": "zeng<PERSON>n", "date": "2025-01-20 12:00:03 +0800", "message": "删除笔记快速评论"}, {"author": "zeng<PERSON>n", "date": "2025-01-20 09:14:16 +0800", "message": "feat： 推送修改"}, {"author": "zeng<PERSON>n", "date": "2025-01-17 09:13:05 +0800", "message": "。。。"}, {"author": "zeng<PERSON>n", "date": "2025-01-16 20:06:31 +0800", "message": "fix : 推送消息统计"}, {"author": "zeng<PERSON>n", "date": "2025-01-16 17:45:50 +0800", "message": "fix :  统计工具"}, {"author": "zeng<PERSON>n", "date": "2025-01-16 17:26:27 +0800", "message": "feat :  推送更新"}, {"author": "zeng<PERSON>n", "date": "2025-01-14 14:11:31 +0800", "message": "修改闪退bug 推送修改"}, {"author": "zeng<PERSON>n", "date": "2025-01-13 17:12:28 +0800", "message": "fix : 融资融券"}, {"author": "zeng<PERSON>n", "date": "2025-01-13 15:44:23 +0800", "message": "fix : 会员中心新增接口状态统计logs"}, {"author": "zeng<PERSON>n", "date": "2025-01-10 11:33:59 +0800", "message": "fix： 处理老款手机真机运行时，在个股详情页面长时间停留会闪退的问题"}, {"author": "zeng<PERSON>n", "date": "2025-01-08 16:10:29 +0800", "message": "fix : 笔记页面支付时去人脸识别，返回后笔记页面在内存中未释放的问题；"}, {"author": "zeng<PERSON>n", "date": "2025-01-08 15:21:32 +0800", "message": "fix ： 笔记无权限也提示关注大咖"}, {"author": "zeng<PERSON>n", "date": "2025-01-08 09:31:33 +0800", "message": "。。。"}, {"author": "zeng<PERSON>n", "date": "2025-01-08 09:31:18 +0800", "message": "fix :  大咖关注和取关"}, {"author": "zeng<PERSON>n", "date": "2025-01-07 14:25:08 +0800", "message": "fix ： 推荐大咖接口修改，相关模型结构和代码逻辑修改"}, {"author": "zeng<PERSON>n", "date": "2025-01-07 10:30:52 +0800", "message": "fix： 替换新接口"}, {"author": "zeng<PERSON>n", "date": "2025-01-06 09:31:23 +0800", "message": "fix: 行情服务h5链接替换"}, {"author": "zeng<PERSON>n", "date": "2024-12-31 15:45:00 +0800", "message": "fix： 网页拉起支付宝支付报错后无法继续下一步的问题"}, {"author": "zeng<PERSON>n", "date": "2024-12-27 12:02:29 +0800", "message": "。。。"}, {"author": "zeng<PERSON>n", "date": "2024-12-26 16:15:14 +0800", "message": "fix ： 解套宝详情页面无法释放问题"}, {"author": "zeng<PERSON>n", "date": "2024-12-25 14:27:14 +0800", "message": "。。。"}, {"author": "zeng<PERSON>n", "date": "2024-12-25 13:31:58 +0800", "message": "..."}, {"author": "zeng<PERSON>n", "date": "2024-12-25 11:14:04 +0800", "message": "fix ： 日夜间模式切换bug"}, {"author": "zeng<PERSON>n", "date": "2024-12-25 09:00:01 +0800", "message": "fix : 夜间模式设置"}, {"author": "zeng<PERSON>n", "date": "2024-12-24 14:37:55 +0800", "message": "...."}, {"author": "zeng<PERSON>n", "date": "2024-12-24 14:30:44 +0800", "message": "fix : 修改个股详情、分时UI"}, {"author": "zeng<PERSON>n", "date": "2024-12-23 15:37:31 +0800", "message": "fix ： 盘中监测跳动异常"}, {"author": "zeng<PERSON>n", "date": "2024-12-23 10:39:45 +0800", "message": "fix ： 笔记支付切换bug、盘前涨停聚显示问题"}, {"author": "zeng<PERSON>n", "date": "2024-12-22 15:57:23 +0800", "message": "Merge commit 'bbabdddec78bd032ae2dab1678a36bc30aad281b' into develop"}, {"author": "zeng<PERSON>n", "date": "2024-12-22 15:52:28 +0800", "message": "fix : 选择卡券后微信支付没有抵扣"}, {"author": "zeng<PERSON>n", "date": "2024-12-22 15:50:59 +0800", "message": "fix : 搜索闪退"}, {"author": "zeng<PERSON>n", "date": "2024-12-20 17:43:51 +0800", "message": "。。。"}, {"author": "zeng<PERSON>n", "date": "2024-12-20 14:48:47 +0800", "message": "fix : 笔记暂停、微信支付bug"}, {"author": "zeng<PERSON>n", "date": "2024-12-19 19:54:12 +0800", "message": "fix : 龙虎榜、涨停聚焦bug"}, {"author": "zeng<PERSON>n", "date": "2024-12-19 17:22:37 +0800", "message": "fix: 接口更新"}, {"author": "zeng<PERSON>n", "date": "2024-12-19 16:58:46 +0800", "message": "。。。"}, {"author": "zeng<PERSON>n", "date": "2024-12-19 14:05:29 +0800", "message": "fix : 解套宝指标"}, {"author": "zeng<PERSON>n", "date": "2024-12-19 10:09:10 +0800", "message": "。。。"}, {"author": "zeng<PERSON>n", "date": "2024-12-18 20:42:00 +0800", "message": "fix : 股票搜索"}, {"author": "zeng<PERSON>n", "date": "2024-12-18 17:22:55 +0800", "message": "fix : 解套宝bug"}, {"author": "zeng<PERSON>n", "date": "2024-12-18 17:00:13 +0800", "message": "fix : 解套宝bug"}, {"author": "zeng<PERSON>n", "date": "2024-12-18 12:05:04 +0800", "message": "fix : 涨停聚焦显示等问题"}, {"author": "zeng<PERSON>n", "date": "2024-12-17 19:27:57 +0800", "message": "feat : 解套宝完成"}, {"author": "zeng<PERSON>n", "date": "2024-12-16 20:09:55 +0800", "message": "..."}, {"author": "zeng<PERSON>n", "date": "2024-12-16 16:23:19 +0800", "message": "feat : 解套宝"}, {"author": "zeng<PERSON>n", "date": "2024-12-14 20:47:21 +0800", "message": "feat: 解套宝 fix bug;"}, {"author": "zeng<PERSON>n", "date": "2024-12-12 14:24:27 +0800", "message": "feat : 解套宝"}, {"author": "zeng<PERSON>n", "date": "2024-12-12 10:02:43 +0800", "message": "。。。"}, {"author": "zeng<PERSON>n", "date": "2024-12-12 10:01:03 +0800", "message": "fix：更新微信sdk"}, {"author": "zeng<PERSON>n", "date": "2024-12-12 09:45:43 +0800", "message": "Merge commit '1a9f6be8746a53f3f5968218c4fcc668a73421d8'"}, {"author": "zeng<PERSON>n", "date": "2024-12-11 19:54:11 +0800", "message": "fix : 笔记微信支付"}, {"author": "zeng<PERSON>n", "date": "2024-12-11 14:40:25 +0800", "message": "fix: 修改payment方式"}, {"author": "zeng<PERSON>n", "date": "2024-12-11 11:52:45 +0800", "message": "feat : 笔记增加暂停购买"}, {"author": "zeng<PERSON>n", "date": "2024-12-11 11:15:19 +0800", "message": "feat : 完成行情列表页涨停聚焦"}, {"author": "zeng<PERSON>n", "date": "2024-12-10 15:09:20 +0800", "message": "。。。"}, {"author": "zeng<PERSON>n", "date": "2024-12-10 15:09:07 +0800", "message": "feat : 完成笔记、大咖详情页，关注大咖及时提醒"}, {"author": "zeng<PERSON>n", "date": "2024-12-09 16:10:21 +0800", "message": "完成龙虎榜"}, {"author": "zeng<PERSON>n", "date": "2024-12-06 14:23:03 +0800", "message": "fix : 顺向滚动广告条只有1条时禁止滚动"}, {"author": "zeng<PERSON>n", "date": "2024-12-06 13:48:39 +0800", "message": "fix : GmenuController点击消失时闪退的问题"}, {"author": "zeng<PERSON>n", "date": "2024-12-06 10:31:03 +0800", "message": "fix : 提问页咨询股票改成我们的接口"}, {"author": "zeng<PERSON>n", "date": "2024-12-05 19:19:24 +0800", "message": "fix : 指标设置页面状态栏颜色"}, {"author": "zeng<PERSON>n", "date": "2024-12-05 16:12:24 +0800", "message": "fix : 夜间模式切换bug"}, {"author": "zeng<PERSON>n", "date": "2024-12-05 15:39:10 +0800", "message": "fix : 话题页面UI等"}, {"author": "zeng<PERSON>n", "date": "2024-12-05 09:56:24 +0800", "message": "..."}, {"author": "zeng<PERSON>n", "date": "2024-12-05 09:38:01 +0800", "message": "fix : 底部tabbar适配暗色模式"}, {"author": "zeng<PERSON>n", "date": "2024-12-04 15:25:57 +0800", "message": "fix : 暗黑模式与旧版暗夜蒙版的适配"}, {"author": "zeng<PERSON>n", "date": "2024-12-04 14:07:25 +0800", "message": "。。。"}, {"author": "zeng<PERSON>n", "date": "2024-12-04 11:31:25 +0800", "message": "fix bug"}, {"author": "zeng<PERSON>n", "date": "2024-12-04 08:57:54 +0800", "message": "。。。"}, {"author": "zeng<PERSON>n", "date": "2024-12-03 16:10:52 +0800", "message": "。。。"}, {"author": "zeng<PERSON>n", "date": "2024-12-03 16:10:36 +0800", "message": "feat : 自选页面适配暗黑模式"}, {"author": "zeng<PERSON>n", "date": "2024-12-03 09:09:07 +0800", "message": "..."}, {"author": "zeng<PERSON>n", "date": "2024-12-02 19:29:42 +0800", "message": "..."}, {"author": "zeng<PERSON>n", "date": "2024-12-02 17:56:47 +0800", "message": "fix : 个股详情页bug"}, {"author": "zeng<PERSON>n", "date": "2024-12-02 17:56:28 +0800", "message": "fix： 增加指标服务器地址"}, {"author": "zeng<PERSON>n", "date": "2024-12-02 15:14:04 +0800", "message": "fix : 行情列表页绘图bug"}, {"author": "zeng<PERSON>n", "date": "2024-12-02 09:01:25 +0800", "message": "fix: bug"}, {"author": "zeng<PERSON>n", "date": "2024-11-27 20:24:28 +0800", "message": "fix : 模板显示不出来"}, {"author": "zeng<PERSON>n", "date": "2024-11-27 17:08:36 +0800", "message": "..."}, {"author": "zeng<PERSON>n", "date": "2024-11-27 17:05:42 +0800", "message": "..."}, {"author": "zeng<PERSON>n", "date": "2024-11-27 15:58:12 +0800", "message": "fix: 行情列表适配暗黑模式"}, {"author": "zeng<PERSON>n", "date": "2024-11-27 13:32:50 +0800", "message": "fix : 完成行情列表修改"}, {"author": "zeng<PERSON>n", "date": "2024-11-25 12:06:30 +0800", "message": "..."}, {"author": "zeng<PERSON>n", "date": "2024-11-25 10:29:23 +0800", "message": "..."}, {"author": "zeng<PERSON>n", "date": "2024-11-25 10:18:31 +0800", "message": "fix : 个股行情暗黑适配"}, {"author": "zeng<PERSON>n", "date": "2024-11-22 11:04:20 +0800", "message": "fix :  指标选择修改"}, {"author": "zeng<PERSON>n", "date": "2024-11-22 10:40:22 +0800", "message": "fix : 修改指标配色"}, {"author": "zeng<PERSON>n", "date": "2024-11-22 10:13:36 +0800", "message": "feat : 分时五档与明细修改"}, {"author": "zeng<PERSON>n", "date": "2024-11-21 19:21:40 +0800", "message": "fix : 个股详情颜色1"}, {"author": "zeng<PERSON>n", "date": "2024-11-21 18:06:49 +0800", "message": "fix : 删除腾讯云推送"}, {"author": "zeng<PERSON>n", "date": "2024-11-19 14:19:03 +0800", "message": "Merge remote-tracking branch 'origin/3.6.5' into develop"}, {"author": "zeng<PERSON>n", "date": "2024-11-19 14:00:04 +0800", "message": "fix : 区间统计横竖屏显示冲突"}, {"author": "zeng<PERSON>n", "date": "2024-11-19 10:46:37 +0800", "message": "feat : 修改个股详情"}, {"author": "zeng<PERSON>n", "date": "2024-11-15 13:55:22 +0800", "message": "...."}, {"author": "zeng<PERSON>n", "date": "2024-11-13 19:22:47 +0800", "message": "feat : 指标无权限，则少绘制5天数据"}, {"author": "zeng<PERSON>n", "date": "2024-11-13 15:10:38 +0800", "message": "feat : 机构持仓详情增加个股行情"}, {"author": "zeng<PERSON>n", "date": "2024-11-13 11:48:47 +0800", "message": "feat： 修改自选指数"}, {"author": "zeng<PERSON>n", "date": "2024-11-13 09:25:58 +0800", "message": "feat： 大宗交易未完成"}, {"author": "zeng<PERSON>n", "date": "2024-11-13 09:25:05 +0800", "message": "..."}, {"author": "zeng<PERSON>n", "date": "2024-11-12 16:47:24 +0800", "message": "..."}, {"author": "zeng<PERSON>n", "date": "2024-11-12 11:49:05 +0800", "message": "fix：老板块显示问题"}, {"author": "zeng<PERSON>n", "date": "2024-11-12 10:47:22 +0800", "message": "feat : 删除拼音转换"}, {"author": "zeng<PERSON>n", "date": "2024-11-12 10:14:18 +0800", "message": "fix： 赞赏用户头像显示问题"}, {"author": "zeng<PERSON>n", "date": "2024-11-11 16:45:03 +0800", "message": "feat : 完成大咖主页宣传栏"}, {"author": "zeng<PERSON>n", "date": "2024-11-11 14:56:46 +0800", "message": "feat: 修改SGAdvertScrollView，让其支持attrbuteString设置"}, {"author": "zeng<PERSON>n", "date": "2024-11-11 11:56:57 +0800", "message": "feat : 增加换绑微信功能"}, {"author": "zeng<PERSON>n", "date": "2024-11-11 09:54:44 +0800", "message": "feat : 话题列表修改"}, {"author": "zeng<PERSON>n", "date": "2024-11-10 15:50:41 +0800", "message": "feat : 增加包月服务协议跳转"}, {"author": "zeng<PERSON>n", "date": "2024-11-10 15:37:55 +0800", "message": "fix : 策略只有主图的指标没有附图指标，选中该策略后切换个股，策略没有反显"}, {"author": "zeng<PERSON>n", "date": "2024-11-10 10:40:59 +0800", "message": "fix：阿里云推送回退"}, {"author": "zeng<PERSON>n", "date": "2024-11-08 16:22:10 +0800", "message": "fix: 删除第一创业sdk"}, {"author": "zeng<PERSON>n", "date": "2024-11-08 16:08:05 +0800", "message": "。。。"}, {"author": "zeng<PERSON>n", "date": "2024-11-08 15:34:15 +0800", "message": "fix ： 电子发票"}, {"author": "zeng<PERSON>n", "date": "2024-11-08 14:25:04 +0800", "message": "feat : 加载服务器cordova.js，取消wk_registerScheme相关逻辑"}, {"author": "zeng<PERSON>n", "date": "2024-11-08 14:24:28 +0800", "message": "fix ： 个人信息登记bug"}, {"author": "zeng<PERSON>n", "date": "2024-11-06 12:28:12 +0800", "message": "..."}, {"author": "zeng<PERSON>n", "date": "2024-11-06 11:02:47 +0800", "message": "Merge remote-tracking branch 'origin/腾讯云推送' into develop"}, {"author": "zeng<PERSON>n", "date": "2024-11-06 10:50:47 +0800", "message": "feat： 完成新版个人信息登记"}, {"author": "zeng<PERSON>n", "date": "2024-11-05 16:18:43 +0800", "message": "fix : 信息登记修改"}, {"author": "zeng<PERSON>n", "date": "2024-11-05 10:24:48 +0800", "message": "..."}, {"author": "zeng<PERSON>n", "date": "2024-11-04 20:02:32 +0800", "message": "feat ： 腾讯云移动推送接入完毕"}, {"author": "zeng<PERSON>n", "date": "2024-11-04 12:31:01 +0800", "message": "。。。"}, {"author": "zeng<PERSON>n", "date": "2024-11-04 12:05:49 +0800", "message": "fix : 会员中心增加报错提示"}, {"author": "zeng<PERSON>n", "date": "2024-11-04 11:07:26 +0800", "message": "fix ： 删除阿里云相关"}, {"author": "zeng<PERSON>n", "date": "2024-11-04 10:40:23 +0800", "message": "fix : 删除WTS"}, {"author": "zeng<PERSON>n", "date": "2024-11-04 10:13:51 +0800", "message": "。。。"}, {"author": "zeng<PERSON>n", "date": "2024-11-01 11:37:54 +0800", "message": "fix : 退出登录后关注、点赞等状态未更新bug"}, {"author": "zeng<PERSON>n", "date": "2024-11-01 10:37:07 +0800", "message": "Merge branch 'develop' of http://git.wts99.cn/web/apps/djc-app-ios into develop"}, {"author": "zeng<PERSON>n", "date": "2024-11-01 10:35:26 +0800", "message": "。。。"}, {"author": "zeng<PERSON>n", "date": "2024-10-30 21:39:39 +0800", "message": "fix： 初始化失败也可以进入首页"}, {"author": "zeng<PERSON>n", "date": "2024-10-30 11:34:00 +0800", "message": "话题列表回退"}, {"author": "zeng<PERSON>n", "date": "2024-10-29 18:13:38 +0800", "message": "fix bug"}, {"author": "zeng<PERSON>n", "date": "2024-10-29 17:22:54 +0800", "message": "。。。"}, {"author": "zeng<PERSON>n", "date": "2024-10-29 17:22:22 +0800", "message": "Merge branch 'develop' of http://git.wts99.cn/web/apps/djc-app-ios into develop"}, {"author": "zeng<PERSON>n", "date": "2024-10-29 17:22:13 +0800", "message": "。。。"}, {"author": "zeng<PERSON>n", "date": "2024-10-29 17:18:17 +0800", "message": "风险提示书参数修改"}, {"author": "zeng<PERSON>n", "date": "2024-10-29 17:16:36 +0800", "message": "fix ：风险提示书去掉参数"}, {"author": "zeng<PERSON>n", "date": "2024-10-29 14:12:16 +0800", "message": "fix : 屏蔽弹幕功能"}, {"author": "zeng<PERSON>n", "date": "2024-10-29 12:19:50 +0800", "message": "fix : 删除支付宝支付"}, {"author": "zeng<PERSON>n", "date": "2024-10-29 11:45:42 +0800", "message": "fix : 删除账号密码登录、修改密码等"}, {"author": "zeng<PERSON>n", "date": "2024-10-29 11:43:26 +0800", "message": "fix： 回复我的，跳转错误"}, {"author": "zeng<PERSON>n", "date": "2024-10-29 10:46:17 +0800", "message": "fix： 隐藏账号密码登录，内购审核时需要看到"}, {"author": "zeng<PERSON>n", "date": "2024-10-29 10:25:51 +0800", "message": "feat: 接入腾讯云移动推送"}, {"author": "zeng<PERSON>n", "date": "2024-10-28 19:45:57 +0800", "message": "fix： 部分h5页面地址替换"}, {"author": "zeng<PERSON>n", "date": "2024-10-28 18:51:51 +0800", "message": "fix：修改首页每日任务进度接口调用逻辑"}, {"author": "zeng<PERSON>n", "date": "2024-10-28 16:12:35 +0800", "message": "fix：登录时移除请求指标、模板的接口"}, {"author": "zeng<PERSON>n", "date": "2024-10-28 15:48:03 +0800", "message": "fix： 初始化接口等待时增加假启动图"}, {"author": "zeng<PERSON>n", "date": "2024-10-28 15:09:38 +0800", "message": "fix： 修改启动逻辑，appInit接口成功后才能进入App"}, {"author": "zeng<PERSON>n", "date": "2024-10-25 16:52:38 +0800", "message": "。。。"}, {"author": "zeng<PERSON>n", "date": "2024-10-25 16:45:28 +0800", "message": "fix : 修改会员中心数据结构，处理闪退"}, {"author": "zeng<PERSON>n", "date": "2024-10-25 16:35:56 +0800", "message": "fix : 删除无效内容"}, {"author": "zeng<PERSON>n", "date": "2024-10-25 11:46:09 +0800", "message": "fix : F10概念折叠状态未保持"}, {"author": "zeng<PERSON>n", "date": "2024-10-25 09:01:34 +0800", "message": "fix bug"}, {"author": "zeng<PERSON>n", "date": "2024-10-24 16:55:14 +0800", "message": "fix : 已下架策略显示"}, {"author": "zeng<PERSON>n", "date": "2024-10-24 16:45:56 +0800", "message": "..."}, {"author": "zeng<PERSON>n", "date": "2024-10-24 15:18:45 +0800", "message": "fix : xcode16上maskView同名属性闪退"}, {"author": "zeng<PERSON>n", "date": "2024-10-24 12:39:02 +0800", "message": "fix :  推后queryPushSwitch接口请求，修改提示显示逻辑"}, {"author": "zeng<PERSON>n", "date": "2024-10-24 09:45:10 +0800", "message": "fix: 用户数据同步接口增加定时"}, {"author": "zeng<PERSON>n", "date": "2024-10-24 09:17:05 +0800", "message": "feat : 关注大咖增加本地缓存"}, {"author": "zeng<PERSON>n", "date": "2024-10-23 18:38:29 +0800", "message": "feat : 问股点赞"}, {"author": "zeng<PERSON>n", "date": "2024-10-23 18:36:50 +0800", "message": "feat : 笔记收藏缓存"}, {"author": "zeng<PERSON>n", "date": "2024-10-23 17:51:46 +0800", "message": "feat : 修改笔记点赞逻辑，增加本地缓存"}, {"author": "zeng<PERSON>n", "date": "2024-10-23 14:18:47 +0800", "message": "fix: 热门搜索、经常访问接口替换"}, {"author": "zeng<PERSON>n", "date": "2024-10-23 12:07:49 +0800", "message": "fix : 我的策略显示bug"}, {"author": "zeng<PERSON>n", "date": "2024-10-23 11:12:41 +0800", "message": "。。。"}, {"author": "zeng<PERSON>n", "date": "2024-10-23 11:09:49 +0800", "message": "fix : 替换我的会员、每日任务进度接口"}, {"author": "zeng<PERSON>n", "date": "2024-10-23 11:02:15 +0800", "message": "feat : 热门搜索增加6小时延时"}, {"author": "zeng<PERSON>n", "date": "2024-10-22 17:14:19 +0800", "message": "fix：FixColumnCell多行显示下拉箭头bug"}, {"author": "zeng<PERSON>n", "date": "2024-10-22 16:08:04 +0800", "message": "fix 策略相关bug"}, {"author": "zeng<PERSON>n", "date": "2024-10-22 11:50:07 +0800", "message": "fix：日历筛选框在多层级页面上可能显示不了底部栏"}, {"author": "zeng<PERSON>n", "date": "2024-10-22 10:00:56 +0800", "message": "fix ： 策略显示逻辑、涨停强度涨停数根据筛选修改"}, {"author": "zeng<PERSON>n", "date": "2024-10-21 17:55:38 +0800", "message": "feat : 策略栏目显示、隐藏动态控制"}, {"author": "zeng<PERSON>n", "date": "2024-10-21 16:39:57 +0800", "message": "fix : 回退配色"}, {"author": "zeng<PERSON>n", "date": "2024-10-21 16:00:58 +0800", "message": "feat : 策略分享"}, {"author": "zeng<PERSON>n", "date": "2024-10-21 15:01:24 +0800", "message": "feat : 首页策略、个人中心我的策略"}, {"author": "zeng<PERSON>n", "date": "2024-10-18 18:04:56 +0800", "message": "..."}, {"author": "zeng<PERSON>n", "date": "2024-10-18 18:01:29 +0800", "message": "feat : 笔记超期解锁提醒"}, {"author": "zeng<PERSON>n", "date": "2024-10-18 16:21:49 +0800", "message": "feat : 完成个股详情页大宗交易UI"}, {"author": "zeng<PERSON>n", "date": "2024-10-18 09:44:47 +0800", "message": "。。。"}, {"author": "zeng<PERSON>n", "date": "2024-10-18 09:44:13 +0800", "message": "fix： 话题列表"}, {"author": "zeng<PERSON>n", "date": "2024-10-18 09:22:20 +0800", "message": "fix : 话题列表"}, {"author": "zeng<PERSON>n", "date": "2024-10-16 14:16:55 +0800", "message": "fix : 搜索股票显示标签"}, {"author": "zeng<PERSON>n", "date": "2024-10-16 11:04:09 +0800", "message": "..."}, {"author": "zeng<PERSON>n", "date": "2024-10-16 11:02:25 +0800", "message": "Merge remote-tracking branch 'origin/master' into develop"}, {"author": "zeng<PERSON>n", "date": "2024-10-16 11:01:23 +0800", "message": "fix : 会员权限指标进入行情也的状态刷新问题"}, {"author": "zeng<PERSON>n", "date": "2024-10-15 15:09:07 +0800", "message": "fix :  指数详情下方显示问题"}, {"author": "zeng<PERSON>n", "date": "2024-10-15 14:19:53 +0800", "message": "fix : 搜索股票替换"}, {"author": "zeng<PERSON>n", "date": "2024-10-15 14:15:40 +0800", "message": "。。。"}, {"author": "zeng<PERSON>n", "date": "2024-10-15 10:01:16 +0800", "message": "fix : 忘记密码改成联系客服"}, {"author": "zeng<PERSON>n", "date": "2024-10-14 17:03:16 +0800", "message": "fix : 修改横屏时指标付费提示UI"}, {"author": "zeng<PERSON>n", "date": "2024-10-14 14:48:48 +0800", "message": "fix : 搜索股票用我们接口替换优品数据"}, {"author": "zeng<PERSON>n", "date": "2024-10-14 13:41:33 +0800", "message": "fix ： 涨停聚焦日期切换放在顶部"}, {"author": "zeng<PERSON>n", "date": "2024-10-12 11:40:43 +0800", "message": "..."}, {"author": "zeng<PERSON>n", "date": "2024-10-12 11:10:34 +0800", "message": "。。。"}, {"author": "zeng<PERSON>n", "date": "2024-10-12 10:27:17 +0800", "message": "fix : 正式环境最强风口数据未显示问题"}, {"author": "zeng<PERSON>n", "date": "2024-10-11 16:56:02 +0800", "message": "fix ： 内购时登录页面显示问题"}, {"author": "zeng<PERSON>n", "date": "2024-10-11 15:41:13 +0800", "message": "fix : 删除参考仓位"}, {"author": "zeng<PERSON>n", "date": "2024-10-11 13:57:58 +0800", "message": "fix :  涨停聚焦日历，当日不可选择"}, {"author": "zeng<PERSON>n", "date": "2024-10-11 10:16:12 +0800", "message": "fix : 首次进入App，首页进入工具栏二级页面返回后，首页会自动滚动的bug"}, {"author": "zeng<PERSON>n", "date": "2024-10-11 09:43:03 +0800", "message": "fix ： 修改st相关内容"}, {"author": "zeng<PERSON>n", "date": "2024-10-10 19:33:41 +0800", "message": "fix : 涨停强度、最强风口只有今日数据请求优品接口"}, {"author": "zeng<PERSON>n", "date": "2024-10-10 16:02:37 +0800", "message": "fix : ios18上更新弹框无法点击"}, {"author": "zeng<PERSON>n", "date": "2024-10-10 10:48:31 +0800", "message": "fix：最强风口折叠、跳转点击bug"}, {"author": "zeng<PERSON>n", "date": "2024-10-09 17:02:59 +0800", "message": "...."}, {"author": "zeng<PERSON>n", "date": "2024-10-09 16:35:59 +0800", "message": "fix : 增加所属行业"}, {"author": "zeng<PERSON>n", "date": "2024-10-09 16:09:55 +0800", "message": "。。。"}, {"author": "zeng<PERSON>n", "date": "2024-10-09 15:07:00 +0800", "message": "fix : 涨停聚焦刷新bug"}, {"author": "zeng<PERSON>n", "date": "2024-10-08 17:13:16 +0800", "message": "..."}, {"author": "zeng<PERSON>n", "date": "2024-10-08 16:43:26 +0800", "message": "fix : 修改podfile，处理pods下更新"}, {"author": "zeng<PERSON>n", "date": "2024-10-08 14:51:33 +0800", "message": "fix： 涨停聚焦、机构持仓bug"}, {"author": "zeng<PERSON>n", "date": "2024-09-30 17:39:51 +0800", "message": "fix bug feat : 增加机构持仓跳转"}, {"author": "zeng<PERSON>n", "date": "2024-09-29 20:00:54 +0800", "message": "..."}, {"author": "zeng<PERSON>n", "date": "2024-09-29 17:43:17 +0800", "message": "fix : FMLHBFixColumnCell多行点击箭头显示问题 feat : 板块地图支持板块下股票地图 fix：分时图调整五档位置未更新问题"}, {"author": "zeng<PERSON>n", "date": "2024-09-29 11:45:34 +0800", "message": "feat : 增加首页登录提醒"}, {"author": "zeng<PERSON>n", "date": "2024-09-29 11:05:44 +0800", "message": "..."}, {"author": "zeng<PERSON>n", "date": "2024-09-27 16:38:42 +0800", "message": "fix : 涨停聚焦bug feat：基金持仓、个股详情板块弹窗"}, {"author": "zeng<PERSON>n", "date": "2024-09-26 17:44:03 +0800", "message": "fix : 修改个股监控、连板天梯等"}, {"author": "zeng<PERSON>n", "date": "2024-09-26 10:26:11 +0800", "message": "fix : 自选列表变色修改"}, {"author": "zeng<PERSON>n", "date": "2024-09-26 10:24:37 +0800", "message": "fix : 机构持仓、个股详情指标及时刷新"}, {"author": "zeng<PERSON>n", "date": "2024-09-24 16:40:15 +0800", "message": "fix ： 增加研报、公告协议"}, {"author": "zeng<PERSON>n", "date": "2024-09-24 15:42:02 +0800", "message": "1.完成云指标排序； 2.修改支付确认书流程； 3.付费指标显示问题；"}, {"author": "zeng<PERSON>n", "date": "2024-09-23 19:06:33 +0800", "message": "fix ： 支付个人信息和风险测评"}, {"author": "zeng<PERSON>n", "date": "2024-09-23 15:47:38 +0800", "message": "fix ： 处理指标详情说明显示换行错误的问题"}, {"author": "zeng<PERSON>n", "date": "2024-09-23 15:26:13 +0800", "message": "fix : 登录图形验证码"}, {"author": "zeng<PERSON>n", "date": "2024-09-23 15:06:59 +0800", "message": "fix :  登录增加图形验证码"}, {"author": "zeng<PERSON>n", "date": "2024-09-23 12:06:14 +0800", "message": "fix：指标购买状态更新、分时指标显示等问题"}, {"author": "zeng<PERSON>n", "date": "2024-09-23 11:07:11 +0800", "message": "feat : 完成已购服务"}, {"author": "zeng<PERSON>n", "date": "2024-09-19 16:56:39 +0800", "message": "。。。"}, {"author": "zeng<PERSON>n", "date": "2024-09-19 16:56:27 +0800", "message": "fix： 策略bug"}, {"author": "zeng<PERSON>n", "date": "2024-09-19 15:49:25 +0800", "message": "fix： 策略bug"}, {"author": "zeng<PERSON>n", "date": "2024-09-19 10:47:49 +0800", "message": "feat : 机构持仓"}, {"author": "zeng<PERSON>n", "date": "2024-09-14 11:57:05 +0800", "message": "fix： 策略"}, {"author": "zeng<PERSON>n", "date": "2024-09-14 09:20:32 +0800", "message": "fix : 策略说明视频播放"}, {"author": "zeng<PERSON>n", "date": "2024-09-13 17:08:06 +0800", "message": "fix : 删除短线精灵"}, {"author": "zeng<PERSON>n", "date": "2024-09-13 16:03:20 +0800", "message": "fix 策略 bug"}, {"author": "zeng<PERSON>n", "date": "2024-09-13 13:37:36 +0800", "message": "fix : 策略bug"}, {"author": "zeng<PERSON>n", "date": "2024-09-12 11:39:29 +0800", "message": "feat : 策略"}, {"author": "zeng<PERSON>n", "date": "2024-09-11 13:35:18 +0800", "message": "。。。"}, {"author": "zeng<PERSON>n", "date": "2024-09-11 13:35:05 +0800", "message": "feat : 策略UI完成"}, {"author": "zeng<PERSON>n", "date": "2024-09-10 15:32:58 +0800", "message": "feat : 涨停聚焦接口对接"}, {"author": "zeng<PERSON>n", "date": "2024-09-09 09:31:13 +0800", "message": "...."}, {"author": "zeng<PERSON>n", "date": "2024-09-09 09:31:00 +0800", "message": "Merge branch 'develop' of http://git.wts99.cn/web/apps/djc-app-ios into develop"}, {"author": "zeng<PERSON>n", "date": "2024-09-06 16:07:47 +0800", "message": "feat : 策略模板"}, {"author": "zeng<PERSON>n", "date": "2024-09-06 10:01:47 +0800", "message": "策略模板"}, {"author": "zeng<PERSON>n", "date": "2024-09-05 19:59:46 +0800", "message": "fix : 指标bug feat：指标支付"}, {"author": "zeng<PERSON>n", "date": "2024-09-05 10:22:41 +0800", "message": "feat : 完成禁止屏幕截屏"}, {"author": "zeng<PERSON>n", "date": "2024-09-04 16:48:46 +0800", "message": "fix：修改未登录时指标权限判断问题"}, {"author": "zeng<PERSON>n", "date": "2024-09-04 15:36:16 +0800", "message": "。。。"}, {"author": "zeng<PERSON>n", "date": "2024-09-04 10:42:24 +0800", "message": "Merge remote-tracking branch 'origin/云指标导入' into develop"}, {"author": "zeng<PERSON>n", "date": "2024-09-04 09:45:00 +0800", "message": "feat：策略"}, {"author": "zeng<PERSON>n", "date": "2024-09-04 09:44:00 +0800", "message": "fix ： 指标权限修改"}, {"author": "zeng<PERSON>n", "date": "2024-09-03 17:54:30 +0800", "message": "fix : 模板未返回主图指标情况，默认显示均线"}, {"author": "zeng<PERSON>n", "date": "2024-09-03 17:46:10 +0800", "message": "fix ：处理后台将云指标配置了MA、BOLL这种默认指标时，K线图上绘制错乱的问题"}, {"author": "zeng<PERSON>n", "date": "2024-09-03 16:01:52 +0800", "message": "。。。"}, {"author": "zeng<PERSON>n", "date": "2024-09-02 19:43:41 +0800", "message": "fix：指标权限，加锁"}, {"author": "zeng<PERSON>n", "date": "2024-09-02 19:43:06 +0800", "message": "fix： 没有权限的指标，选择包含该权限的模板时，无法显示的问题。 warning :可能会产生其它刷新问题"}, {"author": "zeng<PERSON>n", "date": "2024-09-02 18:03:24 +0800", "message": "fix： 没有权限的指标，选择包含该权限的模板时，无法显示的问题。 warning :可能会产生其它刷新问题"}, {"author": "zeng<PERSON>n", "date": "2024-09-02 18:00:49 +0800", "message": "fix :  不重要"}, {"author": "zeng<PERSON>n", "date": "2024-09-02 14:47:39 +0800", "message": "fix ： 指标模板切换判断是否回显的逻辑"}, {"author": "zeng<PERSON>n", "date": "2024-09-02 14:46:28 +0800", "message": "fix ： 搜索页面标签、互动UI等"}, {"author": "zeng<PERSON>n", "date": "2024-08-30 16:38:24 +0800", "message": "fix : 笔记二级评论UI修改"}, {"author": "zeng<PERSON>n", "date": "2024-08-30 15:41:12 +0800", "message": "feat : 涨停聚焦弹框"}, {"author": "zeng<PERSON>n", "date": "2024-08-30 10:10:43 +0800", "message": "fix ： 股票概念及其弹框修改"}, {"author": "zeng<PERSON>n", "date": "2024-08-30 10:10:13 +0800", "message": "fix : 修改涨停聚焦UI"}, {"author": "zeng<PERSON>n", "date": "2024-08-28 11:55:01 +0800", "message": "。。。"}, {"author": "zeng<PERSON>n", "date": "2024-08-28 11:54:40 +0800", "message": "feat：模板指标"}, {"author": "zeng<PERSON>n", "date": "2024-08-23 09:35:08 +0800", "message": "Merge commit 'b94d78b3e7729b15738fd47d0cc095eca0a1b684' into develop"}, {"author": "zeng<PERSON>n", "date": "2024-08-22 17:31:58 +0800", "message": "fix :  评论不直接展示"}, {"author": "zeng<PERSON>n", "date": "2024-08-22 15:44:02 +0800", "message": "fix :  财务分析数据默认显示、股东股本修改结构增加无数据展位图"}, {"author": "zeng<PERSON>n", "date": "2024-08-22 10:07:46 +0800", "message": "fix : 行情左右切换时，股东股本绘制错误"}, {"author": "zeng<PERSON>n", "date": "2024-08-22 09:47:38 +0800", "message": "fix : 参考止损、参考操作价格显示逻辑修改 fix : 板块异动修改"}, {"author": "zeng<PERSON>n", "date": "2024-08-21 17:59:18 +0800", "message": "fix : F10股东股本只有1条数据时，没有绘制 fix: 默认请求地址无法覆盖"}, {"author": "zeng<PERSON>n", "date": "2024-08-21 15:33:23 +0800", "message": "fix：修改资金流向、板块数据请求频率"}, {"author": "zeng<PERSON>n", "date": "2024-08-21 15:11:49 +0800", "message": "fix UI 细节"}, {"author": "zeng<PERSON>n", "date": "2024-08-21 14:53:29 +0800", "message": "feat ： 个人中心增加兑换码"}, {"author": "zeng<PERSON>n", "date": "2024-08-21 14:08:08 +0800", "message": "fix：今日涨速最快查看更多，排序"}, {"author": "zeng<PERSON>n", "date": "2024-08-20 16:15:39 +0800", "message": "fix ：行情首页热门板块增加定时"}, {"author": "zeng<PERSON>n", "date": "2024-08-20 16:14:34 +0800", "message": "fix : 个人中心UI"}, {"author": "zeng<PERSON>n", "date": "2024-08-20 11:45:37 +0800", "message": "fix : 笔记评论闪退"}, {"author": "zeng<PERSON>n", "date": "2024-08-20 11:36:59 +0800", "message": "fix : 综合搜索页偶现的闪退"}, {"author": "zeng<PERSON>n", "date": "2024-08-20 10:57:46 +0800", "message": "fix : 行情域名配置"}, {"author": "zeng<PERSON>n", "date": "2024-08-20 09:59:41 +0800", "message": "fix ：F10无数据时闪退"}, {"author": "zeng<PERSON>n", "date": "2024-08-20 09:57:13 +0800", "message": "fix：推票支持多支"}, {"author": "zeng<PERSON>n", "date": "2024-08-19 18:37:18 +0800", "message": "fix：个别股票股利支付率无法绘制的问题"}, {"author": "zeng<PERSON>n", "date": "2024-08-19 14:58:41 +0800", "message": "fix : 股池改名"}, {"author": "zeng<PERSON>n", "date": "2024-08-16 15:12:41 +0800", "message": "fix :  股池初始排序失效"}, {"author": "zeng<PERSON>n", "date": "2024-08-16 11:55:26 +0800", "message": "fix ： 擒龙笔记战法修改UI"}, {"author": "zeng<PERSON>n", "date": "2024-08-16 11:40:19 +0800", "message": "fix :  修改UIButton+ImageTitleSpacing，处理在未提前获取到frame情况下，图文间隔不生效的问题"}, {"author": "zeng<PERSON>n", "date": "2024-08-16 10:47:00 +0800", "message": "fix : 股池切换时数据接口被多次调用的问题"}, {"author": "zeng<PERSON>n", "date": "2024-08-16 09:56:32 +0800", "message": "fix : 决策价投改为价值龙头"}, {"author": "zeng<PERSON>n", "date": "2024-08-16 09:53:10 +0800", "message": "fix : 资金流向列表与地图增加数据都为0时的站位图"}, {"author": "zeng<PERSON>n", "date": "2024-08-15 16:07:51 +0800", "message": "fix : 新股日历UI、买五卖五、股池默认排序"}, {"author": "zeng<PERSON>n", "date": "2024-08-15 13:59:33 +0800", "message": "首页UI修改、资金流向数据很多时上下拉偶尔闪退、股池UI细节"}, {"author": "zeng<PERSON>n", "date": "2024-08-15 10:57:38 +0800", "message": "feat : 完成新版股池"}, {"author": "zeng<PERSON>n", "date": "2024-08-12 15:51:29 +0800", "message": "fix ：增加了preview后的pdf链接打不开的问题"}, {"author": "zeng<PERSON>n", "date": "2024-08-12 13:51:12 +0800", "message": "feat : 增加板块跳转协议"}, {"author": "zeng<PERSON>n", "date": "2024-08-12 12:26:10 +0800", "message": "fix : 板块五日分时图接口有些数据未返回昨收造成的绘制错误"}, {"author": "zeng<PERSON>n", "date": "2024-08-12 11:00:47 +0800", "message": "指标商城入口回退"}, {"author": "zeng<PERSON>n", "date": "2024-08-12 09:38:36 +0800", "message": "。。。"}, {"author": "zeng<PERSON>n", "date": "2024-08-09 17:08:48 +0800", "message": "云指标导入2"}, {"author": "zeng<PERSON>n", "date": "2024-08-09 17:08:27 +0800", "message": "云指标导入1"}, {"author": "zeng<PERSON>n", "date": "2024-08-08 19:57:11 +0800", "message": "fix : 板块分时不要显示均价线"}, {"author": "zeng<PERSON>n", "date": "2024-08-08 19:35:48 +0800", "message": "fix : 计算板块量比与绘制"}, {"author": "zeng<PERSON>n", "date": "2024-08-08 15:56:15 +0800", "message": "fix : 板块K线显示不对"}, {"author": "zeng<PERSON>n", "date": "2024-08-08 15:40:11 +0800", "message": "fix ： 资金风云地图偏移、底部提示框偶尔显示不出来的问题"}, {"author": "zeng<PERSON>n", "date": "2024-08-08 13:43:33 +0800", "message": "fix : 板块五日分时显示时间不对"}, {"author": "zeng<PERSON>n", "date": "2024-08-08 10:46:52 +0800", "message": "fix : 9:10-9:30板块不显示数据"}, {"author": "zeng<PERSON>n", "date": "2024-08-07 19:36:14 +0800", "message": "fix ： 板块收盘显示不对"}, {"author": "zeng<PERSON>n", "date": "2024-08-07 17:17:33 +0800", "message": "fix : 板块分时缺少叠加、涨跌家数、数据格式问题"}, {"author": "zeng<PERSON>n", "date": "2024-08-06 14:14:12 +0800", "message": "fix：板块相关接口定时不准、列表页滑动卡顿、有时页面显示网络错误等问题"}, {"author": "zeng<PERSON>n", "date": "2024-08-06 10:12:41 +0800", "message": "代码返回退"}, {"author": "zeng<PERSON>n", "date": "2024-08-01 16:15:52 +0800", "message": "个股详情回退"}, {"author": "zeng<PERSON>n", "date": "2024-08-01 10:20:43 +0800", "message": "指标商城回退"}, {"author": "zeng<PERSON>n", "date": "2024-07-31 19:07:35 +0800", "message": "..."}, {"author": "zeng<PERSON>n", "date": "2024-07-31 17:26:45 +0800", "message": "fix： 个别笔记高度不对"}, {"author": "zeng<PERSON>n", "date": "2024-07-31 15:54:02 +0800", "message": "..."}, {"author": "zeng<PERSON>n", "date": "2024-07-31 15:45:30 +0800", "message": "fix : 删除人脸认证提示页面"}, {"author": "zeng<PERSON>n", "date": "2024-07-31 13:47:09 +0800", "message": "fix bug"}, {"author": "zeng<PERSON>n", "date": "2024-07-31 10:40:27 +0800", "message": "fix : 人脸识别失败增加提示"}, {"author": "zeng<PERSON>n", "date": "2024-07-30 19:27:43 +0800", "message": "。。。"}, {"author": "zeng<PERSON>n", "date": "2024-07-30 18:15:17 +0800", "message": "fix : 上线审核不能看到微信登录"}, {"author": "zeng<PERSON>n", "date": "2024-07-30 17:54:38 +0800", "message": "fix : 小程序授权"}, {"author": "zeng<PERSON>n", "date": "2024-07-30 16:58:34 +0800", "message": "fix : 修改版本号"}, {"author": "zeng<PERSON>n", "date": "2024-07-30 16:08:29 +0800", "message": "fix :  防止登录页面重复弹出"}, {"author": "zeng<PERSON>n", "date": "2024-07-30 11:58:09 +0800", "message": "板块回退3"}, {"author": "zeng<PERSON>n", "date": "2024-07-30 11:41:04 +0800", "message": "板块回退2"}, {"author": "zeng<PERSON>n", "date": "2024-07-30 11:25:30 +0800", "message": "板块回退1"}, {"author": "zeng<PERSON>n", "date": "2024-07-30 09:53:04 +0800", "message": "fix : 人脸识别失败状态"}, {"author": "zeng<PERSON>n", "date": "2024-07-29 18:06:56 +0800", "message": "fix bug"}, {"author": "zeng<PERSON>n", "date": "2024-07-29 17:27:10 +0800", "message": "fix bug"}, {"author": "zeng<PERSON>n", "date": "2024-07-29 16:48:27 +0800", "message": "...."}, {"author": "zeng<PERSON>n", "date": "2024-07-29 16:42:30 +0800", "message": "fix : faceEnd 增加  requestNo"}, {"author": "zeng<PERSON>n", "date": "2024-07-29 15:20:57 +0800", "message": "feat : 自定义push样式的present动画，方便页面直接返回"}, {"author": "zeng<PERSON>n", "date": "2024-07-29 13:50:02 +0800", "message": "fix bug"}, {"author": "zeng<PERSON>n", "date": "2024-07-29 11:29:55 +0800", "message": "fix bug"}, {"author": "zeng<PERSON>n", "date": "2024-07-27 17:19:49 +0800", "message": "fix ： 修改注销账号"}, {"author": "zeng<PERSON>n", "date": "2024-07-27 15:33:33 +0800", "message": "fix bug"}, {"author": "zeng<PERSON>n", "date": "2024-07-27 11:36:54 +0800", "message": "fix bug"}, {"author": "zeng<PERSON>n", "date": "2024-07-26 17:23:24 +0800", "message": "fix bug"}, {"author": "zeng<PERSON>n", "date": "2024-07-26 16:05:32 +0800", "message": "fix bug"}, {"author": "zeng<PERSON>n", "date": "2024-07-26 09:44:52 +0800", "message": "fix : 删除重置密码等"}, {"author": "zeng<PERSON>n", "date": "2024-07-26 08:57:18 +0800", "message": "feat ： 删除二要素输入页面等"}, {"author": "zeng<PERSON>n", "date": "2024-07-25 16:29:06 +0800", "message": "fix : 修改删除账户逻辑"}, {"author": "zeng<PERSON>n", "date": "2024-07-25 14:04:10 +0800", "message": "fix : 修改WXApi接入方式，处理无法更改统一管理delegate feat: 新增绑定微信"}, {"author": "zeng<PERSON>n", "date": "2024-07-24 17:47:42 +0800", "message": "fix : 板块涨跌分布宽度布局"}, {"author": "zeng<PERSON>n", "date": "2024-07-24 17:18:27 +0800", "message": "fix : 板块定时刷新、跳转等bug"}, {"author": "zeng<PERSON>n", "date": "2024-07-24 16:07:18 +0800", "message": "fix : 板块刷新频率"}, {"author": "zeng<PERSON>n", "date": "2024-07-24 14:39:57 +0800", "message": "fix :  人脸认证流程"}, {"author": "zeng<PERSON>n", "date": "2024-07-23 19:55:34 +0800", "message": "feat : 用户中心改造"}, {"author": "zeng<PERSON>n", "date": "2024-07-22 14:48:32 +0800", "message": "fix : 板块相关bug"}, {"author": "zeng<PERSON>n", "date": "2024-07-22 10:07:29 +0800", "message": "feat : 完成登录改版 UI"}, {"author": "zeng<PERSON>n", "date": "2024-07-19 16:58:45 +0800", "message": "feat : 删除一键登录，修改登录页面"}, {"author": "zeng<PERSON>n", "date": "2024-07-17 17:36:04 +0800", "message": "fix : 修改老板块无法显示的问题、成分股显示排序问题"}, {"author": "zeng<PERSON>n", "date": "2024-07-17 11:27:30 +0800", "message": "fix : 板块搜索"}, {"author": "zeng<PERSON>n", "date": "2024-07-12 16:33:48 +0800", "message": "feat：个股详情概念板块数据刷新、详情跳转等"}, {"author": "zeng<PERSON>n", "date": "2024-07-12 10:44:37 +0800", "message": "fix : 盘中自选股背景色会无法显示的问题"}, {"author": "zeng<PERSON>n", "date": "2024-07-12 10:22:29 +0800", "message": "fix : 板块分时、行情请求刷新问题"}, {"author": "zeng<PERSON>n", "date": "2024-07-11 09:41:16 +0800", "message": "。。。"}, {"author": "zeng<PERSON>n", "date": "2024-07-10 13:36:59 +0800", "message": "Merge remote-tracking branch 'origin/3.5.2发版'"}, {"author": "zeng<PERSON>n", "date": "2024-07-09 09:22:23 +0800", "message": "feat ： 板块分时图"}, {"author": "zeng<PERSON>n", "date": "2024-07-05 14:57:43 +0800", "message": "feat : 完成板块成分股替换"}, {"author": "zeng<PERSON>n", "date": "2024-07-03 16:41:00 +0800", "message": "feat : 完成资金风云"}, {"author": "zeng<PERSON>n", "date": "2024-07-03 12:52:52 +0800", "message": "feat : 板块、资金流向列表"}, {"author": "zeng<PERSON>n", "date": "2024-07-02 10:20:54 +0800", "message": "fix : 完成板块首页"}, {"author": "zeng<PERSON>n", "date": "2024-07-01 16:36:29 +0800", "message": "fix： 未登录状态下，前端交互navigator.general.getUserInfo 拿不到数据的问题处理"}, {"author": "zeng<PERSON>n", "date": "2024-06-28 12:01:18 +0800", "message": "fix : 笔记页面未释放"}, {"author": "zeng<PERSON>n", "date": "2024-06-27 20:09:43 +0800", "message": "fix : 修改部分 tableVIew 初次加载显示了无数据占位图、登录账号在综合搜索页添加股票后 cell 状态未更新问题"}, {"author": "zeng<PERSON>n", "date": "2024-06-23 13:00:08 +0800", "message": "fix：统一 TwoLabelView"}, {"author": "zeng<PERSON>n", "date": "2024-06-21 17:47:08 +0800", "message": "。。。"}, {"author": "zeng<PERSON>n", "date": "2024-06-21 17:44:57 +0800", "message": "fix ：20 日主力资金流向 数据错误"}, {"author": "zeng<PERSON>n", "date": "2024-06-21 16:53:02 +0800", "message": "fix : 新股日历，中签结果排序"}, {"author": "zeng<PERSON>n", "date": "2024-06-21 16:45:32 +0800", "message": "fix bug"}, {"author": "zeng<PERSON>n", "date": "2024-06-21 11:18:36 +0800", "message": "。。。"}, {"author": "zeng<PERSON>n", "date": "2024-06-20 15:56:00 +0800", "message": "fix : 输入内容进行搜索，切换股票，搜索框内容没有清空"}, {"author": "zeng<PERSON>n", "date": "2024-06-20 15:45:52 +0800", "message": "fix ： 隐藏点金、擒龙学院"}, {"author": "zeng<PERSON>n", "date": "2024-06-20 14:39:02 +0800", "message": "fix 个股详情 bug"}, {"author": "zeng<PERSON>n", "date": "2024-06-20 09:21:05 +0800", "message": "fix bug"}, {"author": "zeng<PERSON>n", "date": "2024-06-19 14:45:12 +0800", "message": "fix : 股票涨跌颜色等 bug"}, {"author": "zeng<PERSON>n", "date": "2024-06-19 09:40:08 +0800", "message": "fix bug"}, {"author": "zeng<PERSON>n", "date": "2024-06-18 14:21:15 +0800", "message": "fix : 自选股数据更新背景色加上动画"}, {"author": "zeng<PERSON>n", "date": "2024-06-18 10:07:41 +0800", "message": "feat : 完成头像库"}, {"author": "zeng<PERSON>n", "date": "2024-06-17 16:19:47 +0800", "message": "fix : 股本结构上拉无法加载"}, {"author": "zeng<PERSON>n", "date": "2024-06-17 15:16:49 +0800", "message": "fix : 自选公告研报请求股票数据去重"}, {"author": "zeng<PERSON>n", "date": "2024-06-17 14:18:18 +0800", "message": "feat ： 公告、研报、互动"}, {"author": "zeng<PERSON>n", "date": "2024-06-15 14:14:15 +0800", "message": "feat : 完成自选公告、研报，个股研报、互动"}, {"author": "zeng<PERSON>n", "date": "2024-06-12 20:30:13 +0800", "message": "fix : 游资榜，左侧TableView高度计算不准的问题"}, {"author": "zeng<PERSON>n", "date": "2024-06-12 20:01:05 +0800", "message": "fix : 重写FMFLHBFixColumn，修改为自动计算高度、增加底部自定义view"}, {"author": "zeng<PERSON>n", "date": "2024-06-12 10:11:20 +0800", "message": "。。。"}, {"author": "zeng<PERSON>n", "date": "2024-06-11 17:46:28 +0800", "message": "fix： 活动专区UI、自选页面数据醒目显示"}, {"author": "zeng<PERSON>n", "date": "2024-06-11 14:51:27 +0800", "message": "feat ： 个股详情基因统计"}, {"author": "zeng<PERSON>n", "date": "2024-06-07 16:34:53 +0800", "message": "fix ： 修改评级统计、股东股本、分红融资中长按显示图与页面滑动冲突问题"}, {"author": "zeng<PERSON>n", "date": "2024-06-07 13:59:31 +0800", "message": "fix ： 20日主流净流入长按图形绘制"}, {"author": "zeng<PERSON>n", "date": "2024-06-07 11:11:41 +0800", "message": " fix: 资金接口替换"}, {"author": "zeng<PERSON>n", "date": "2024-06-06 14:00:13 +0800", "message": "fix ： F10从其他栏目切换到公司概况、分红融资，必须下拉一下才能向上划动的bug"}, {"author": "zeng<PERSON>n", "date": "2024-06-04 17:53:00 +0800", "message": "fix : 新股日历、F10等bug"}, {"author": "zeng<PERSON>n", "date": "2024-06-04 11:11:33 +0800", "message": "fix : 新股日历待申购排序bug"}, {"author": "zeng<PERSON>n", "date": "2024-06-03 16:11:23 +0800", "message": "fix : 新股日历bug"}, {"author": "zeng<PERSON>n", "date": "2024-06-03 15:03:05 +0800", "message": "Merge commit '873c093a08783d1e4736388d912e7a411e8768c8'"}, {"author": "zeng<PERSON>n", "date": "2024-05-31 13:31:03 +0800", "message": "fix  : ..."}, {"author": "zeng<PERSON>n", "date": "2024-05-31 11:45:56 +0800", "message": "fix : 新股日历、F10 bug"}, {"author": "zeng<PERSON>n", "date": "2024-05-30 19:37:38 +0800", "message": "fix : F10相关bug"}, {"author": "zeng<PERSON>n", "date": "2024-05-30 19:37:20 +0800", "message": "..."}, {"author": "zeng<PERSON>n", "date": "2024-05-30 17:09:48 +0800", "message": "fix ： 笔记详情多票bug"}, {"author": "zeng<PERSON>n", "date": "2024-05-30 15:14:54 +0800", "message": "feat : 新股日历"}, {"author": "zeng<PERSON>n", "date": "2024-05-30 14:04:42 +0800", "message": "3.5.6"}, {"author": "zeng<PERSON>n", "date": "2024-05-29 20:13:53 +0800", "message": "feat : 完成新股日历列表"}, {"author": "zeng<PERSON>n", "date": "2024-05-29 19:24:42 +0800", "message": "fix : 股利支付率图标计算与绘制bug"}, {"author": "zeng<PERSON>n", "date": "2024-05-29 16:35:45 +0800", "message": "fix ： F10相关bug"}, {"author": "zeng<PERSON>n", "date": "2024-05-28 17:50:25 +0800", "message": "fix : F10相关bug"}, {"author": "zeng<PERSON>n", "date": "2024-05-28 15:36:49 +0800", "message": "fix : 分红融资bug"}, {"author": "zeng<PERSON>n", "date": "2024-05-27 09:35:17 +0800", "message": "fix ：分红融资bug"}, {"author": "zeng<PERSON>n", "date": "2024-05-24 15:32:26 +0800", "message": "fix : 推票支持多只"}, {"author": "zeng<PERSON>n", "date": "2024-05-23 17:27:43 +0800", "message": "feat : F10分红融资完成"}, {"author": "zeng<PERSON>n", "date": "2024-05-22 12:02:14 +0800", "message": "..."}, {"author": "zeng<PERSON>n", "date": "2024-05-22 09:34:00 +0800", "message": "fix : 股票名跟安卓统一显示"}, {"author": "zeng<PERSON>n", "date": "2024-05-22 09:27:52 +0800", "message": "fix ： 股票名跟安卓统一显示短命"}, {"author": "zeng<PERSON>n", "date": "2024-05-21 20:43:00 +0800", "message": "fix ： 指标商城bug"}, {"author": "zeng<PERSON>n", "date": "2024-05-21 16:27:46 +0800", "message": "fix : 修改消息中心接口"}, {"author": "zeng<PERSON>n", "date": "2024-05-21 14:05:26 +0800", "message": "fix : 注册不提示强密码"}, {"author": "zeng<PERSON>n", "date": "2024-05-21 12:03:32 +0800", "message": "feat : 3.5.5"}, {"author": "zeng<PERSON>n", "date": "2024-05-21 11:49:50 +0800", "message": "fix: 新用户礼包修改"}, {"author": "zeng<PERSON>n", "date": "2024-05-21 11:42:06 +0800", "message": "feat : 新增日活统计"}, {"author": "zeng<PERSON>n", "date": "2024-05-21 11:28:04 +0800", "message": "feat : 优品新版F10"}, {"author": "zeng<PERSON>n", "date": "2024-05-17 17:20:06 +0800", "message": "fix ： 指标商城相关bug"}, {"author": "zeng<PERSON>n", "date": "2024-05-17 14:05:21 +0800", "message": "fix ： 增加Lookin、修改新用户礼包赠送为动态"}, {"author": "zeng<PERSON>n", "date": "2024-05-17 11:34:16 +0800", "message": "fix ： 注册不提示强密码"}, {"author": "zeng<PERSON>n", "date": "2024-05-16 14:23:10 +0800", "message": "fix ： 替换新版优品F10"}, {"author": "zeng<PERSON>n", "date": "2024-05-16 10:25:12 +0800", "message": "fix : 指标商城协议跳转、未登录等"}, {"author": "zeng<PERSON>n", "date": "2024-05-15 17:02:24 +0800", "message": "fix : 增加指标锁"}, {"author": "zeng<PERSON>n", "date": "2024-05-15 11:17:53 +0800", "message": "fix ： 指标导入"}, {"author": "zeng<PERSON>n", "date": "2024-05-14 17:39:27 +0800", "message": "fix : 修改导入网络指标的逻辑"}, {"author": "zeng<PERSON>n", "date": "2024-05-13 19:04:53 +0800", "message": "..."}, {"author": "zeng<PERSON>n", "date": "2024-05-13 17:52:00 +0800", "message": "feat ： 指标详情"}, {"author": "zeng<PERSON>n", "date": "2024-05-13 16:49:08 +0800", "message": "fix ： 修改昵称页面，手动收起键盘后页面卡死的问题"}, {"author": "zeng<PERSON>n", "date": "2024-05-13 14:26:41 +0800", "message": "fix ： 评论不直接显示"}, {"author": "zeng<PERSON>n", "date": "2024-05-11 22:16:52 +0800", "message": "fix : 注册时禁止修改昵称"}, {"author": "zeng<PERSON>n", "date": "2024-05-11 12:24:57 +0800", "message": "fix ： 禁止修改头像"}, {"author": "zeng<PERSON>n", "date": "2024-05-11 09:10:07 +0800", "message": "Merge remote-tracking branch 'origin/3.5.2发版'"}, {"author": "zeng<PERSON>n", "date": "2024-05-10 17:34:21 +0800", "message": "fix ： 修改头像提示问题"}, {"author": "zeng<PERSON>n", "date": "2024-05-10 17:15:15 +0800", "message": "feat ： 指标商城"}, {"author": "zeng<PERSON>n", "date": "2024-05-10 15:12:57 +0800", "message": "fix : 修改头像bug"}, {"author": "zeng<PERSON>n", "date": "2024-05-10 10:34:04 +0800", "message": "fix ： 日活统计优化"}, {"author": "zeng<PERSON>n", "date": "2024-05-09 18:48:21 +0800", "message": "fix ： 东莞证券开户"}, {"author": "zeng<PERSON>n", "date": "2024-05-09 18:24:23 +0800", "message": "fix ： 东莞证券开户"}, {"author": "zeng<PERSON>n", "date": "2024-05-09 18:11:57 +0800", "message": "fix : 东莞证券开户"}, {"author": "zeng<PERSON>n", "date": "2024-05-09 18:05:45 +0800", "message": "Merge branch 'master' of http://git.wts99.cn/web/apps/djc-app-ios"}, {"author": "zeng<PERSON>n", "date": "2024-05-09 18:02:10 +0800", "message": "fix : 东莞证券开户问题"}, {"author": "zeng<PERSON>n", "date": "2024-05-09 17:29:03 +0800", "message": "feat ： 新股日历"}, {"author": "zeng<PERSON>n", "date": "2024-05-09 17:02:46 +0800", "message": "fix : 东莞证券开户提示浏览器版本不支持"}, {"author": "zeng<PERSON>n", "date": "2024-05-08 14:34:38 +0800", "message": "fix : 修改日活统计"}, {"author": "zeng<PERSON>n", "date": "2024-05-08 10:33:55 +0800", "message": "feat ： 新股日历"}, {"author": "zeng<PERSON>n", "date": "2024-05-08 10:33:19 +0800", "message": "fix : 东莞证券人脸验证授权失败的问题"}, {"author": "zeng<PERSON>n", "date": "2024-05-06 14:36:20 +0800", "message": "feat : 完成日活统计"}, {"author": "zeng<PERSON>n", "date": "2024-05-06 14:30:59 +0800", "message": "feat : 指标商城、新股日历UI"}, {"author": "zeng<PERSON>n", "date": "2024-04-25 14:55:14 +0800", "message": "feat : 指标商城"}, {"author": "zeng<PERSON>n", "date": "2024-04-24 16:04:01 +0800", "message": "fix ： f10闪退、UI显示bug"}, {"author": "zeng<PERSON>n", "date": "2024-04-22 17:59:08 +0800", "message": "fix ： 没有数据时不要显示上拉加载更多"}, {"author": "zeng<PERSON>n", "date": "2024-04-18 16:31:55 +0800", "message": "Merge commit 'b8343b8191b69c30b3765fc4ba1bdc1d85e9663f' into develop"}, {"author": "zeng<PERSON>n", "date": "2024-04-18 16:21:47 +0800", "message": "Merge branch 'master' of http://git.wts99.cn/web/apps/djc-app-ios"}, {"author": "zeng<PERSON>n", "date": "2024-04-18 16:16:48 +0800", "message": "fix : 分红融资bug"}, {"author": "zeng<PERSON>n", "date": "2024-04-16 09:43:52 +0800", "message": "fix bug"}, {"author": "zeng<PERSON>n", "date": "2024-04-09 18:02:43 +0800", "message": "fix : 修改DropdownMenu、没网时搜索页会出现搜索不到股票的问题、高管资料支持换行"}, {"author": "zeng<PERSON>n", "date": "2024-04-08 17:59:40 +0800", "message": "fix bug"}, {"author": "zeng<PERSON>n", "date": "2024-04-07 16:36:26 +0800", "message": "feat : 公告详情 fix： 部分bug"}, {"author": "zeng<PERSON>n", "date": "2024-04-03 10:35:47 +0800", "message": "feat ： 增加privacyInfo，处理Missing API declaration问题"}, {"author": "zeng<PERSON>n", "date": "2024-04-02 17:37:31 +0800", "message": "feat : 完成评级统计点击后的图形绘制"}, {"author": "zeng<PERSON>n", "date": "2024-04-02 16:17:21 +0800", "message": "feat ： 完成股东情况点击绘制股东数据"}, {"author": "zeng<PERSON>n", "date": "2024-04-02 09:08:03 +0800", "message": "fix bug"}, {"author": "zeng<PERSON>n", "date": "2024-03-29 13:39:20 +0800", "message": "feat : 自选资讯增加无数据占位图"}, {"author": "zeng<PERSON>n", "date": "2024-03-29 11:14:44 +0800", "message": "fix : 删除股票新闻"}, {"author": "zeng<PERSON>n", "date": "2024-03-29 11:01:19 +0800", "message": "Merge commit '7fdd0c8be79a0267dcdb73401d04a97d20042c63'"}, {"author": "zeng<PERSON>n", "date": "2024-03-29 10:58:05 +0800", "message": "..."}, {"author": "zeng<PERSON>n", "date": "2024-03-29 10:41:53 +0800", "message": ".。。"}, {"author": "zeng<PERSON>n", "date": "2024-03-29 10:33:57 +0800", "message": "fix : 删除新闻"}, {"author": "zeng<PERSON>n", "date": "2024-03-28 21:49:51 +0800", "message": "fix bug"}, {"author": "zeng<PERSON>n", "date": "2024-03-28 14:24:32 +0800", "message": "fix : 笔记推票支持多个"}, {"author": "zeng<PERSON>n", "date": "2024-03-28 13:43:48 +0800", "message": "fix ： F10切换子View后，限制子View内部滚动"}, {"author": "zeng<PERSON>n", "date": "2024-03-28 13:43:10 +0800", "message": "feat ： F10研报详情"}, {"author": "zeng<PERSON>n", "date": "2024-03-27 16:25:28 +0800", "message": "feat：完成操盘必读接口对接"}, {"author": "zeng<PERSON>n", "date": "2024-03-27 09:07:21 +0800", "message": "feat ： 完成市场观点"}, {"author": "zeng<PERSON>n", "date": "2024-03-26 16:32:49 +0800", "message": "feat : 股本股东接口"}, {"author": "zeng<PERSON>n", "date": "2024-03-25 10:11:30 +0800", "message": "feat : 完成概念题材等"}, {"author": "zeng<PERSON>n", "date": "2024-03-21 16:58:37 +0800", "message": "Merge branch 'develop' of http://git.wts99.cn/web/apps/djc-app-ios into develop"}, {"author": "zeng<PERSON>n", "date": "2024-03-21 16:58:32 +0800", "message": "feat : 完成公司概况接口"}, {"author": "zeng<PERSON>n", "date": "2024-03-20 19:19:20 +0800", "message": "fix bug"}, {"author": "zeng<PERSON>n", "date": "2024-03-20 16:56:57 +0800", "message": "。。。"}, {"author": "zeng<PERSON>n", "date": "2024-03-20 16:42:07 +0800", "message": "Merge branch 'develop' of http://git.wts99.cn/web/apps/djc-app-ios into develop"}, {"author": "zeng<PERSON>n", "date": "2024-03-20 16:42:01 +0800", "message": "fix : 龙虎榜机构bug 1. 日期区间筛选，总市值、流通市值排序不对； 2. 日期区间筛选多只股票，排序后只显示第一天数据； 3. 跳转股票详情，提示错误"}, {"author": "zeng<PERSON>n", "date": "2024-03-19 15:41:39 +0800", "message": "Merge branch 'develop' of http://git.wts99.cn/web/apps/djc-app-ios into develop"}, {"author": "zeng<PERSON>n", "date": "2024-03-19 15:41:34 +0800", "message": "feat : 操盘必读UI完成"}, {"author": "zeng<PERSON>n", "date": "2024-03-18 17:43:31 +0800", "message": "Merge branch 'develop' of http://git.wts99.cn/web/apps/djc-app-ios into develop"}, {"author": "zeng<PERSON>n", "date": "2024-03-18 17:43:25 +0800", "message": "feat： F10公司概况等"}, {"author": "zeng<PERSON>n", "date": "2024-03-14 19:59:30 +0800", "message": "Merge branch 'develop' of http://git.wts99.cn/web/apps/djc-app-ios into develop"}, {"author": "zeng<PERSON>n", "date": "2024-03-14 19:57:39 +0800", "message": "feat : 公司概况基本资料、发行相关UI fix：iOS12系统上龙虎榜股票详情显示问题不对、数据无法展示"}, {"author": "zeng<PERSON>n", "date": "2024-03-13 22:26:36 +0800", "message": "Merge branch 'develop' of http://git.wts99.cn/web/apps/djc-app-ios into develop"}, {"author": "zeng<PERSON>n", "date": "2024-03-13 16:02:43 +0800", "message": "..."}, {"author": "zeng<PERSON>n", "date": "2024-03-13 15:55:08 +0800", "message": "..."}, {"author": "zeng<PERSON>n", "date": "2024-03-13 14:51:00 +0800", "message": "..."}, {"author": "zeng<PERSON>n", "date": "2024-03-13 14:44:29 +0800", "message": "feat ： F10概念题材、大事提醒UI"}, {"author": "zeng<PERSON>n", "date": "2024-03-12 16:38:24 +0800", "message": "feat : f10开发"}, {"author": "zeng<PERSON>n", "date": "2024-03-12 08:59:41 +0800", "message": " fix: 证券开户交易bug"}, {"author": "zeng<PERSON>n", "date": "2024-03-11 17:40:40 +0800", "message": "fix ： 个人中心所在地闪退"}, {"author": "zeng<PERSON>n", "date": "2024-03-11 17:15:22 +0800", "message": "Merge remote-tracking branch 'origin/fixKaihuBug' into develop"}, {"author": "zeng<PERSON>n", "date": "2024-03-11 17:09:47 +0800", "message": "fix ： 行情图中间空白"}, {"author": "zeng<PERSON>n", "date": "2024-03-11 16:17:37 +0800", "message": "。。。"}, {"author": "zeng<PERSON>n", "date": "2024-03-11 15:25:40 +0800", "message": "Merge branch 'develop' of http://git.wts99.cn/web/apps/djc-app-ios into develop"}, {"author": "zeng<PERSON>n", "date": "2024-03-11 15:25:36 +0800", "message": "fix : 拉新有礼闪退、所属概念点击不了、行情编辑隐藏等问题"}, {"author": "zeng<PERSON>n", "date": "2024-03-11 15:02:22 +0800", "message": "。。。"}, {"author": "zeng<PERSON>n", "date": "2024-03-08 14:36:40 +0800", "message": "fix ： 大咖头像背景色偏差问题"}, {"author": "zeng<PERSON>n", "date": "2024-03-08 11:19:45 +0800", "message": "fix ： 优品行情修改"}, {"author": "zeng<PERSON>n", "date": "2024-03-06 20:32:16 +0800", "message": "Merge branch 'develop' of http://git.wts99.cn/web/apps/djc-app-ios into develop"}, {"author": "zeng<PERSON>n", "date": "2024-03-06 20:32:12 +0800", "message": "fix ：机构庆典文字绘制问题"}, {"author": "zeng<PERSON>n", "date": "2024-03-04 16:03:56 +0800", "message": "Merge branch 'develop' of http://git.wts99.cn/web/apps/djc-app-ios into develop"}, {"author": "zeng<PERSON>n", "date": "2024-03-04 16:03:52 +0800", "message": "fix ： 龙虎榜k线18点前不显示最后一根柱子"}, {"author": "zeng<PERSON>n", "date": "2024-03-01 17:41:23 +0800", "message": "fix : K线设置横屏时，文字、图片变小的问题；"}, {"author": "zeng<PERSON>n", "date": "2024-03-01 17:18:52 +0800", "message": "。。。"}, {"author": "zeng<PERSON>n", "date": "2024-03-01 17:03:24 +0800", "message": "feat : 优品行情第五版导入"}, {"author": "zeng<PERSON>n", "date": "2024-03-01 16:46:59 +0800", "message": "feat : 优品行情第五版导入"}, {"author": "zeng<PERSON>n", "date": "2024-03-01 15:10:12 +0800", "message": "fix : 笔记详情段间距"}, {"author": "zeng<PERSON>n", "date": "2024-03-01 14:02:35 +0800", "message": "fix ： 非MA BOLL指标下显示区间统计"}, {"author": "zeng<PERSON>n", "date": "2024-03-01 13:35:21 +0800", "message": "Merge branch 'develop' of http://git.wts99.cn/web/apps/djc-app-ios into develop"}, {"author": "zeng<PERSON>n", "date": "2024-03-01 13:35:16 +0800", "message": "fix ： 在非MA、BOLL指标下显示区间统计"}, {"author": "zeng<PERSON>n", "date": "2024-03-01 10:25:54 +0800", "message": "fix : 股票列表，无标签数据的cell内部可滑动问题"}, {"author": "zeng<PERSON>n", "date": "2024-02-29 20:26:41 +0800", "message": "Merge branch 'develop' of http://git.wts99.cn/web/apps/djc-app-ios into develop"}, {"author": "zeng<PERSON>n", "date": "2024-02-29 20:26:34 +0800", "message": "fix : 股票列表点击事件冲突、显示UI错误"}, {"author": "zeng<PERSON>n", "date": "2024-02-29 16:15:22 +0800", "message": "Merge branch 'develop' of http://git.wts99.cn/web/apps/djc-app-ios into develop"}, {"author": "zeng<PERSON>n", "date": "2024-02-29 16:15:17 +0800", "message": "fix : 游资列表股票数量过滤"}, {"author": "zeng<PERSON>n", "date": "2024-02-29 15:53:29 +0800", "message": "fix ： 删除净买入占比"}, {"author": "zeng<PERSON>n", "date": "2024-02-29 15:40:44 +0800", "message": "fix ： 笔记详情字号字体修改"}, {"author": "zeng<PERSON>n", "date": "2024-02-29 14:16:32 +0800", "message": "Merge branch 'develop' of http://git.wts99.cn/web/apps/djc-app-ios into develop"}, {"author": "zeng<PERSON>n", "date": "2024-02-29 14:16:28 +0800", "message": "feat ： 关于我们增加备案号、处理未开通交易的证券公司情况"}, {"author": "zeng<PERSON>n", "date": "2024-02-29 10:13:51 +0800", "message": "fix : 修改开户列表页未开通交易的公司显示了交易 删除updebug"}, {"author": "zeng<PERSON>n", "date": "2024-02-28 18:06:32 +0800", "message": "fix : 横屏展示股票行情时，限制切换按钮展示"}, {"author": "zeng<PERSON>n", "date": "2024-02-28 17:16:33 +0800", "message": "fix : 个股行情无法侧滑返回"}, {"author": "zeng<PERSON>n", "date": "2024-02-28 15:42:34 +0800", "message": "fix : 龙虎榜行情图定位到上榜日期后会自动偏移的问题、放大后无法滚动到最后柱子"}, {"author": "zeng<PERSON>n", "date": "2024-02-28 13:55:44 +0800", "message": "Merge branch 'develop' of http://git.wts99.cn/web/apps/djc-app-ios into develop"}, {"author": "zeng<PERSON>n", "date": "2024-02-28 13:55:39 +0800", "message": "fix : tagView计算宽度bug、股票列表跳转详情后返回位置偏移 feat : 所属概念弹框"}, {"author": "zeng<PERSON>n", "date": "2024-02-27 17:47:29 +0800", "message": "Merge branch 'develop' of http://git.wts99.cn/web/apps/djc-app-ios into develop"}, {"author": "zeng<PERSON>n", "date": "2024-02-27 17:47:21 +0800", "message": "..."}, {"author": "zeng<PERSON>n", "date": "2024-02-27 15:14:51 +0800", "message": "fix : 优品SDK第四版导入"}, {"author": "zeng<PERSON>n", "date": "2024-02-27 14:23:15 +0800", "message": "fix : 优品第四版SDK替换"}, {"author": "zeng<PERSON>n", "date": "2024-02-27 13:56:04 +0800", "message": "fix ：优品SDK 第四版替换"}, {"author": "zeng<PERSON>n", "date": "2024-02-27 13:35:11 +0800", "message": "。。。"}, {"author": "zeng<PERSON>n", "date": "2024-02-27 10:28:22 +0800", "message": "fix ： 区间统计增加占位图"}, {"author": "zeng<PERSON>n", "date": "2024-02-27 10:08:42 +0800", "message": "Merge branch 'develop' of http://git.wts99.cn/web/apps/djc-app-ios into develop"}, {"author": "zeng<PERSON>n", "date": "2024-02-27 10:08:38 +0800", "message": "fix : 营业部详情添加筛选无数据占位图"}, {"author": "zeng<PERSON>n", "date": "2024-02-26 19:55:15 +0800", "message": "fix： 连榜统计页面无数据、删除切换等"}, {"author": "zeng<PERSON>n", "date": "2024-02-26 15:58:56 +0800", "message": "fix : 机构列表跳转股票详情bug"}, {"author": "zeng<PERSON>n", "date": "2024-02-26 15:36:01 +0800", "message": "Merge branch 'develop' of http://git.wts99.cn/web/apps/djc-app-ios into develop"}, {"author": "zeng<PERSON>n", "date": "2024-02-26 15:35:58 +0800", "message": "fix ： graphView内切换股票时K线图并未更新的问题"}, {"author": "zeng<PERSON>n", "date": "2024-02-26 14:05:15 +0800", "message": " fix: 个股详情区间统计，从选中日开始绘制近几日，近几日数据不够的情况"}, {"author": "zeng<PERSON>n", "date": "2024-02-26 10:01:05 +0800", "message": "fix : 在龙虎榜指定日期列表跳转到个股详情没有定位日期的问题"}, {"author": "zeng<PERSON>n", "date": "2024-02-25 16:39:19 +0800", "message": "Merge branch 'develop' of http://git.wts99.cn/web/apps/djc-app-ios into develop"}, {"author": "zeng<PERSON>n", "date": "2024-02-25 16:39:14 +0800", "message": "fix : 营业部详情页买卖图标显示问题"}, {"author": "zeng<PERSON>n", "date": "2024-02-25 16:10:42 +0800", "message": "fix : 修改区间统计接口与UI"}, {"author": "zeng<PERSON>n", "date": "2024-02-25 15:55:37 +0800", "message": "fix : 股票详情的日期选择"}, {"author": "zeng<PERSON>n", "date": "2024-02-25 15:06:51 +0800", "message": "Merge branch 'develop' of http://git.wts99.cn/web/apps/djc-app-ios into develop"}, {"author": "zeng<PERSON>n", "date": "2024-02-25 15:06:42 +0800", "message": "..."}, {"author": "zeng<PERSON>n", "date": "2024-02-25 14:42:37 +0800", "message": "fix : 营业部详情bug"}, {"author": "zeng<PERSON>n", "date": "2024-02-25 09:11:58 +0800", "message": "fix : 营业部详情页绘制"}, {"author": "zeng<PERSON>n", "date": "2024-02-23 17:10:39 +0800", "message": "Merge branch 'develop' of http://git.wts99.cn/web/apps/djc-app-ios into develop"}, {"author": "zeng<PERSON>n", "date": "2024-02-23 17:10:35 +0800", "message": "fix： 修改营业部详情结构、买卖图标显示bug"}, {"author": "zeng<PERSON>n", "date": "2024-02-23 16:40:07 +0800", "message": "Merge branch 'develop' of http://git.wts99.cn/web/apps/djc-app-ios into develop"}, {"author": "zeng<PERSON>n", "date": "2024-02-23 16:40:04 +0800", "message": "fix bug"}, {"author": "zeng<PERSON>n", "date": "2024-02-23 13:31:52 +0800", "message": "。。。"}, {"author": "zeng<PERSON>n", "date": "2024-02-23 10:35:11 +0800", "message": "fix : 连榜统计绘图bug"}, {"author": "zeng<PERSON>n", "date": "2024-02-23 09:38:45 +0800", "message": " fix : 龙虎榜fixColumnTableView滚动bug"}, {"author": "zeng<PERSON>n", "date": "2024-02-23 09:21:47 +0800", "message": "Merge branch 'develop' of http://git.wts99.cn/web/apps/djc-app-ios into develop"}, {"author": "zeng<PERSON>n", "date": "2024-02-23 09:21:25 +0800", "message": "fix ： 龙虎榜bug"}, {"author": "zeng<PERSON>n", "date": "2024-02-21 16:44:02 +0800", "message": "fix: 龙虎榜个股详情页bug"}, {"author": "zeng<PERSON>n", "date": "2024-02-21 14:53:26 +0800", "message": "Merge branch 'develop' of http://git.wts99.cn/web/apps/djc-app-ios into develop"}, {"author": "zeng<PERSON>n", "date": "2024-02-21 14:52:21 +0800", "message": "fix : 笔记统计bug"}, {"author": "zeng<PERSON>n", "date": "2024-02-21 10:30:59 +0800", "message": "Merge branch 'develop' of http://git.wts99.cn/web/apps/djc-app-ios into develop"}, {"author": "zeng<PERSON>n", "date": "2024-02-21 10:30:54 +0800", "message": "fix :  笔记页面完整阅读统计bug"}, {"author": "zeng<PERSON>n", "date": "2024-02-20 19:41:37 +0800", "message": "feat : 营业部详情"}, {"author": "zeng<PERSON>n", "date": "2024-02-20 17:29:59 +0800", "message": "Merge branch 'develop' of http://git.wts99.cn/web/apps/djc-app-ios into develop"}, {"author": "zeng<PERSON>n", "date": "2024-02-20 17:29:54 +0800", "message": "feat ： 给LHBFixColumnTableView增加默认排序"}, {"author": "zeng<PERSON>n", "date": "2024-02-20 17:01:24 +0800", "message": "Merge branch 'develop' of http://git.wts99.cn/web/apps/djc-app-ios into develop"}, {"author": "zeng<PERSON>n", "date": "2024-02-20 17:01:20 +0800", "message": "feat : 营业部详情"}, {"author": "zeng<PERSON>n", "date": "2024-02-20 12:01:43 +0800", "message": "feat : 龙虎榜修改"}, {"author": "zeng<PERSON>n", "date": "2024-02-19 17:15:59 +0800", "message": "。。。"}, {"author": "zeng<PERSON>n", "date": "2024-02-19 17:04:37 +0800", "message": "Merge branch 'develop' of http://git.wts99.cn/web/apps/djc-app-ios into develop"}, {"author": "zeng<PERSON>n", "date": "2024-02-19 17:04:33 +0800", "message": "fix ： 笔记分享统计不限制次数"}, {"author": "zeng<PERSON>n", "date": "2024-02-19 17:04:06 +0800", "message": "feat： 营业部详情"}, {"author": "zeng<PERSON>n", "date": "2024-02-19 12:33:16 +0800", "message": "feat : 笔记统计"}, {"author": "zeng<PERSON>n", "date": "2024-02-19 12:08:36 +0800", "message": "feat： 笔记统计"}, {"author": "zeng<PERSON>n", "date": "2024-02-19 11:43:15 +0800", "message": "Merge branch 'develop' of http://git.wts99.cn/web/apps/djc-app-ios into develop"}, {"author": "zeng<PERSON>n", "date": "2024-02-19 11:43:10 +0800", "message": "feat ： 笔记统计"}, {"author": "zeng<PERSON>n", "date": "2024-02-19 09:32:14 +0800", "message": "feat ： 连榜统计"}]