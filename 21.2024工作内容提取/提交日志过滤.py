import json
import pandas as pd

# 读取JSON文件
with open("git_logs原版.json", "r", encoding="utf-8") as f:
    data = json.load(f)

# 过滤规则
filtered_data = []
invalid_messages = {"。。。", "...", "....", "....."}  # 需要过滤的无意义消息

for commit in data:
    message = commit["message"].strip()
    date = commit["date"].split(" +0800")[0]  # 去除时区信息

    # 仅保留有意义的提交信息
    if message not in invalid_messages:
        filtered_data.append({
            "时间": date,
            "提交信息": message
        })

# 生成DataFrame并导出Excel
df = pd.DataFrame(filtered_data)
df.to_excel("git_logs_filtered.xlsx", index=False)

print("处理完成！有效记录数量:", len(filtered_data))
print("生成的Excel文件: git_logs_filtered.xlsx")