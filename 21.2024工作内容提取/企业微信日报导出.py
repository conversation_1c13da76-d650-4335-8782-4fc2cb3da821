import requests
import pandas as pd
from datetime import datetime
import time

# 请求URL
url = "https://doc.weixin.qq.com/wework/journal/get_journal_list"

# 查询参数
initial_params = {
    "sid": "1utRVowtYjguYlFpAHc0OAAA",
    "wedoc_xsrf": "1"
}

# 请求头（请确保cookie等信息是最新的）
headers = {
    "content-type": "application/json",
    "accept": "application/json, text/plain, */*",
    "sec-fetch-site": "same-origin",
    "accept-language": "zh-CN,zh-Hans;q=0.9",
    "accept-encoding": "gzip, deflate, br",
    "sec-fetch-mode": "cors",
    "origin": "https://doc.weixin.qq.com",
    "user-agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 18_0_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 wxwork/4.1.32 MicroMessenger/7.0.1 Language/zh ColorScheme/Light wdocs/1.0.0 wdocs-discussion/1.0.0 wdocs_clientdb/1.0.0 ss-rfcardview wdocs-pre-ui/1.0.0",
    "referer": "https://doc.weixin.qq.com/forms/j/AN4A3weBAFwAcoA0AYEAL0oMFpDALGTNj_fork?journaluuid=49tCejytMTvspsRKRZkbfADwuc9GW3Z9CDdDoSbCgCbWHwzcP1pgE554hphDfSYMGP&template_id=3WLJ77G1WMKxyATtit7yKsKn68FeDPW4Nv15FJtG&docid=c2_AN4A3weBAFwAcoA0AYEAL0bW5YBnwnSVj_fork&journal_source=timeline&push_data_start=1737528675359",
    "cookie": "fingerprint=96f9ff25304d4b889bdd7eb393e7ff7d26; hashkey=5f8b775e; traceid=5f8b775ee8; TOK=2379a1edc7568306; TrustID=i___13EC4282-56EF-42FA-8659-197017F4713D-95d8e6365e333c526b52e001cdc0266a___131074; tdoc_uid=13102701682503147; uid=; uid_key=; wedoc_openid=wozbKqDgAAbqPKCPVOJCLExyTR00VnyA; wedoc_sid=1utRVowtYjguYlFpAHc0OAAA; wedoc_sids=13102701682503147&1utRVowtYjguYlFpAHc0OAAA; wedoc_skey=13102701682503147&c2f06e7b25b8f61af39a05dcbb1bcb5d; wedoc_ticket=13102701682503147&CAESINwM-EBxP145OIFkU4cLyetbrF3EO_Wi0IINW1ga-6vw"
}

# 请求体初始数据
initial_data = {
    "lastjournal_id": 0,
    "direction": 1,
    "limit": 20,
    "isconditionquery": True,
    "querydetail": {
        "submission_type": 1,
        "template_id": "3WLJ77G1WMKxyATtit7yKsKn68FeDPW4Nv15FJtG",
        "partyids": [],
        "vids": []
    }
}

# 目标日期（2024-01-01）
target_date = datetime(2024, 1, 1)

# 存储结果的列表
results = []

# 当前lastjournal_id
current_lastjournal_id = 0

while True:
    # 更新请求体中的lastjournal_id
    data = initial_data.copy()
    data["lastjournal_id"] = current_lastjournal_id

    try:
        # 发送POST请求
        response = requests.post(url, params=initial_params, headers=headers, json=data)
        response.raise_for_status()
        response_data = response.json()

        # 检查是否有数据返回
        if not response_data.get("entrys"):
            print("没有更多数据了。")
            break

        for entry in response_data["entrys"]:
            createtime_epoch = entry.get("createtime")
            createtime = datetime.fromtimestamp(createtime_epoch)

            # 检查是否达到目标日期
            if createtime < target_date:
                print("已达到目标日期，停止获取数据。")
                # 退出所有循环
                raise StopIteration

            # 提取“今日工作总结”内容
            content_str = entry.get("content")
            try:
                content_json = eval(content_str)  # 注意：使用eval有安全风险，确保数据来源可信
                detail = content_json.get("detail", "{}")
                detail_json = eval(detail)
                wordings = detail_json.get("wordings", [])
                if wordings:
                    # 假设“今日工作总结”是第一个元素
                    today_summary = wordings[0].strip()
                else:
                    today_summary = ""
            except Exception as e:
                print(f"解析内容时出错: {e}")
                today_summary = ""

            # 添加到结果列表
            results.append({
                "时间": createtime.strftime('%Y-%m-%d %H:%M:%S'),
                "今日工作总结": today_summary
            })

            # 更新current_lastjournal_id为当前journalid，以便下一次请求
            current_lastjournal_id = entry.get("journalid")

        # 如果返回的数据少于limit，说明没有更多数据了
        if len(response_data["entrys"]) < initial_data["limit"]:
            print("所有数据已获取完毕。")
            break

        # 为避免请求过于频繁，可以添加短暂的延时
        time.sleep(1)

    except StopIteration:
        break
    except requests.exceptions.RequestException as e:
        print(f"请求发生错误: {e}")
        break
    except Exception as e:
        print(f"发生意外错误: {e}")
        break

# 创建DataFrame
df = pd.DataFrame(results)

# 将数据写入Excel
excel_file = "日报数据.xlsx"
df.to_excel(excel_file, index=False)
print(f"数据已保存到 {excel_file}")