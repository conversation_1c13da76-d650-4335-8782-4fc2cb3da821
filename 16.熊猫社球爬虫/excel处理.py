import glob
import os
import pandas as pd

# 获取文件夹中所有的Excel文件，并按文件名排序以保证按日期顺序读取
files = sorted(glob.glob('days/*.xlsx'))

# 创建一个空的DataFrame列表来存储每个文件的结果
results = []

# 对于每个文件，读取数据并进行分析
for file in files:
    # 读取数据
    data = pd.read_excel(file)

    # 使用之前有效的值填充"店名"和"店铺ID"列中的NaN，因为它们在Excel中是合并的单元格
    data['店名'].fillna(method='ffill', inplace=True)
    data['店铺ID'].fillna(method='ffill', inplace=True)

    # 计算每个商店的总占用时间
    data['Total Occupied'] = data.iloc[:, 4:].sum(axis=1)
    total_occupied = data.groupby('店名')['Total Occupied'].sum()

    # 计算每个商店的桌子总数
    table_counts = data['店名'].value_counts()

    # 计算每个商店的总占用时间
    total_occupied_times = total_occupied / 2  # 因为每次计数表示半小时

    # 计算每个商店每张桌子的平均占用时间，并保留两位小数
    average_occupied_times = (total_occupied_times / table_counts).round(2)

    # 获取日期作为列名
    date = os.path.basename(file).split('_')[2].split('.')[0]

    # 将结果添加到结果DataFrame列表中
    results.append(average_occupied_times.rename(date))

# 将所有的结果连接在一起
result = pd.concat(results, axis=1)

# 计算每个商店的平均占用时间，并保留两位小数
result['total_day_average_time'] = result.mean(axis=1).round(2)

# 将结果按照总平均占用时间降序排序
result = result.sort_values('total_day_average_time', ascending=False)

# 将结果保存为Excel文件
result.to_excel("average_table_status.xlsx")
