import requests
import schedule
import time
import os
import random
import json
from datetime import datetime, date
from openpyxl import Workbook, load_workbook
from openpyxl.styles import PatternFill

def get_table_status(shop_id):
    url = "https://api.pandaball.cc/api/shop/poolTable"
    headers = {"Host": "api.pandaball.cc",
               "content-type": "application/x-www-form-urlencoded",
               "accept": "*/*",
               "version": "2.0.0",
               "os": "2",
               "packagename": "com.mshy.xmyq",
               "platform": "1",
               "device-name": "iPhone SE2",
               "user-agent": "BilliardBall/2.0.0 (iPhone; iOS 16.1.2; Scale/2.00)",
               "accept-language": "zh-Hans-US;q=1, en-US;q=0.9, zh-Hant-US;q=0.8"}
    params = {"shop_id": shop_id}

    # 尝试发送请求并获取数据，如果请求失败，重试2次，返回None
    retry_times = 3
    for _ in range(retry_times):
        try:
            response = requests.post(url, headers=headers, data=params)
            data = response.json()
            return data
        except requests.exceptions.RequestException as e:
            print(f"Error while requesting shop data: {e}")
            time.sleep(1)  # 重试间隔1秒

    print(f"Failed to get data for shop id {shop_id} after {retry_times} attempts.")
    return None

def job(shop_name, shop_id, run_time):
    print(f"正在获取店铺{shop_name}的数据...")
    data = get_table_status(shop_id)
    if data is None:
        print(f"Failed to get data for shop {shop_name} ({shop_id})")
        return

    status_records = {}
    for item in data['data']['list']:
        table_name = item['name']
        table_id = item['id']
        is_status = item['is_status']
        status_records[table_id] = [table_name, 1 if is_status == 2 else 0]
    print(status_records)

    today = date.today().strftime('%Y%m%d')
    filename = f"table_status_{today}.xlsx"

    if os.path.exists(filename):
        wb = load_workbook(filename)
        ws = wb.active
    else:
        wb = Workbook()
        ws = wb.active
        ws.append(['店名', '店铺ID', '桌名', '桌子ID'])

    # 检查是否已有当前时间的列，没有则添加该列的标题
    if run_time not in [cell.value for cell in ws[1]]:
        ws.cell(row=1, column=ws.max_column+1, value=run_time)

    yellow_fill = PatternFill(start_color='FFFF00', end_color='FFFF00', fill_type='solid')
    col = ws.max_column
    merge_start = ws.max_row + 1
    merge_end = None
    for table_id, table_status in status_records.items():
        row = None
        # 对Excel工作表从第2行（min_row)开始的每一行进行遍历，idx是行的索引（从start这个2开始），r是每一行的内容（是一个元组，包含了该行所有单元格的值）
        for idx, r in enumerate(ws.iter_rows(min_row=2, values_only=True), start=2):
            # 如果已经有table_id所在的行，row赋值idx，表示找到了这行
            if r[3] == table_id:
                row = idx
                break

        # 如果没有找到table_id所在行
        if row is None:
            # 添加一行新的数据
            ws.append([shop_name, shop_id, table_status[0], table_id])
            row = ws.max_row

        merge_end = row  # 合并的结束行
        # 设置当前行中table的状态
        cell = ws.cell(row=row, column=col, value=table_status[1])
        if table_status[1] == 1:
            cell.fill = yellow_fill

    # 处理需要合并的行
    if merge_end is not None and merge_end > merge_start:
        ws.merge_cells(start_row=merge_start, start_column=1, end_row=merge_end, end_column=1)
        ws.merge_cells(start_row=merge_start, start_column=2, end_row=merge_end, end_column=2)

    wb.save(filename)

def job_for_all_shops(run_time):
    # 遍历所有的球馆，调用 job 方法
    for shop in shoplist:
        shop_name = shop['name']
        shop_id = shop['id']
        job(shop_name, shop_id, run_time=run_time)
        # 随机延时 5-10 秒
        time.sleep(random.randint(5, 10))
    print('本轮数据获取完毕！')


# 读取并解析 JSON 文件
with open('qiuguan.json', 'r') as file:
    shops = json.load(file)
    shoplist = sorted(shops, key=lambda x: x['id'])



for i in list(range(11, 24)) + list(range(0, 11)):
    hour_str = f"{i:02d}"
    schedule.every().day.at(f"{hour_str}:01").do(job_for_all_shops, run_time=f"{hour_str}:01")
    schedule.every().day.at(f"{hour_str}:31").do(job_for_all_shops, run_time=f"{hour_str}:31")

while True:
    schedule.run_pending()
    time.sleep(10)



