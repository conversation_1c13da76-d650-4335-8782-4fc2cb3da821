import tkinter as tk
from tkinter import filedialog
import os
from concurrent.futures import ThreadPoolExecutor
import subprocess

# 该函数用于提取音频并将其与视频合并
def extract_and_merge(audio_file, video_file, output_audio, output_video):
    # 提取音频: '-acodec copy' 参数意味着直接复制音频流而不重新编码
    subprocess.run(["ffmpeg", "-i", audio_file, "-acodec", "copy", output_audio])
    # 合并音频和视频: '-c copy' 参数意味着直接复制音视频流而不重新编码
    # '-map 0:v:0' 选择第一个输入文件的视频流
    # '-map 1:a:0' 选择第二个输入文件的音频流
    subprocess.run(["ffmpeg", "-i", video_file, "-i", output_audio, "-c", "copy", "-map", "0:v:0", "-map", "1:a:0", output_video])
    # 移除aac文件
    os.remove(output_audio)


# 该函数用于处理目录中的文件
def process_directory(directory):
    # 创建一个子目录用于存放转换后的文件
    converted_directory = os.path.join(directory, "converted")
    if not os.path.exists(converted_directory):
        os.makedirs(converted_directory)

    files = os.listdir(directory)

    # 使用线程池来并行处理文件
    with ThreadPoolExecutor(max_workers=4) as executor:
        for file in files:
            if file.endswith(".mp4"):
                audio_file = os.path.join(directory, file)
                video_file = os.path.join(directory, file.replace(".mp4", " (1).mp4"))
                output_audio = os.path.join(converted_directory, file.replace(".mp4", ".aac"))
                output_video = os.path.join(converted_directory, file.replace(".mp4", "_final.mp4"))

                # 确保对应的视频文件存在
                if os.path.exists(video_file):
                    # 提交任务到线程池
                    executor.submit(extract_and_merge, audio_file, video_file, output_audio, output_video)

def select_directory():
    directory = filedialog.askdirectory()
    if directory:
        process_directory(directory)
        label_status.config(text="处理完成: " + directory)

# 创建主窗口
root = tk.Tk()
root.title("音视频合并工具")

# 添加标签
label_status = tk.Label(root, text="选择一个目录进行处理")
label_status.pack()

# 添加按钮
button_select = tk.Button(root, text="选择目录", command=select_directory)
button_select.pack()

# 启动GUI循环
root.mainloop()

# # 使用示例
# process_directory("/Users/<USER>/Downloads/需要转换的")
