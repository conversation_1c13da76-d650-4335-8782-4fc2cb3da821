from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import os
import time
import random
import string
import json
import re
# import telegram
import requests
import ddddocr

mypath = os.path.split(os.path.realpath(__file__))[0]
chromedriver = ""
if os.name == "nt":
    chromedriver = mypath + "\chromedriver"
elif os.name == "posix":
    # chromedriver = '/usr/bin/chromedriver'
    chromedriver = '/usr/local/bin/chromedriver'

service = Service(chromedriver)
os.environ["webdriver.chrome.driver"] = chromedriver

options = webdriver.ChromeOptions()
options.add_argument('--no-sandbox')  # 解决DevToolsActivePort文件不存在的报错
options.add_argument('window-size=1920x1080')  # 指定浏览器分辨率
options.add_argument('--disable-gpu')  # 谷歌文档提到需要加上这个属性来规避bug
# chrome_options.add_argument('--hide-scrollbars') #隐藏滚动条, 应对一些特殊页面
# chrome_options.add_argument('blink-settings=imagesEnabled=false') #不加载图片, 提升速度
if os.name == "posix":
    # 浏览器不提供可视化页面. linux下如果系统不支持可视化不加这条会启动失败
    options.add_argument('--headless')

user_ag = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/107.0.0.0 Safari/537.36"
options.add_argument('user-agent=%s' % user_ag)

ocr = ddddocr.DdddOcr(show_ad=False)
delay = 20
new_generated_pwd = ''


# 保存错误页面
def create_error_file(html):
    with open(mypath + "/error.html", "w") as op:
        op.write(html)
        op.close()


# 查找元素是否存在 by class
def find_element_by_class(driver, class_name):
    return WebDriverWait(driver, delay).until(EC.presence_of_element_located((By.CLASS_NAME, class_name)),
                                              '未找到 Class 为 "%s" 的元素' % class_name)


# 查找元素是否存在 by class all
def find_element_by_class_all(driver, class_name):
    return WebDriverWait(driver, delay).until(EC.presence_of_all_elements_located((By.CLASS_NAME, class_name)),
                                              '未找到 Class 为 "%s" 的元素' % class_name)


# 查找元素是否存在 by id
def find_element_by_id(driver, id):
    return WebDriverWait(driver, delay).until(EC.presence_of_element_located((By.ID, id)),
                                              '未找到 id 为 "%s" 的元素' % id)


# 查找元素并且是可用状态 by class
def find_enable_by_class(driver, class_name):
    return WebDriverWait(driver, delay).until(EC.element_to_be_clickable((By.CLASS_NAME, class_name)),
                                              '元素 "%s" 不可用' % class_name)


# 查找元素并且可用 by css
def find_element_by_css(driver, css):
    return WebDriverWait(driver, delay).until(EC.element_to_be_clickable((By.CSS_SELECTOR, css)),
                                              'css元素 "%s" 不可用' % css)


# 发送TG消息
def send_msg(msg):
    print(msg)


# 更新网站上的密码
def update_pwd(api_host, apple_id, passwd):
    if len(api_host) == 0:
        return
    # 以下为PUT请求
    postdatas = {'apple_id': apple_id, 'passwd': passwd}
    r = requests.put(api_host, data=postdatas)
    if r.status_code == requests.codes.ok:
        send_msg(apple_id + ' 密码更新成功！')
    else:
        send_msg('更新密码出错啦：' + r.raise_for_status())


# 创建一个与老密码不同的，特定格式的新密码
def create_specify_pwd(old_pwd):
    while True:
        prefix = 'HaiMac'
        middle = ''.join(random.sample('!@#$*_+=' * 2, 2))
        suffix = ''.join(random.sample(string.digits * 3, 3))
        pwd = prefix + middle + suffix
        if pwd != old_pwd:
            return pwd


# 创建指定位数的随机强密码
def create_random_pwd(passwordLength):
    pw = ''
    # * 2 增加数字和特殊字符出现的概率
    str = string.digits * 2 + string.ascii_letters + '!@#$*_+=' * 2
    while True:
        # 不包含重复字符的密码
        pw = ''.join(random.sample(str, k=passwordLength))
        # 可能包含重复字符的密码
        # pw = ''.join(random.choices(str, k=passwordLength))
        if re.match(r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)', pw):
            print("最终密码：%s" % pw)
            return pw
        else:
            print("密码：%s 强度不够" % pw)


# 随机延时1.0-2.0s
def random_delay():
    # 随机生成1.0到2.0之间的浮点数，小数点后保留1位
    delay = round(random.uniform(1.0, 2.0), 1)
    time.sleep(delay)
    return delay


# 解锁操作
def check_appleid(account):
    apple_id = account["id"]
    birthday = account["birthday"]
    question_dic = account["question"]
    if len(apple_id) < 6 or len(birthday) != 10 or len(question_dic) != 3:
        print("Aplle ID(%s) 信息有误！请检查！" % apple_id)
        return
    api_host = account["api_host"]
    now = round(time.time())
    account["status"] = 1
    try:
        driver = webdriver.Chrome(service=service)  # , options=options 增加这个就不会打开浏览器，待修改
        # 设置全局元素查找的超时时间
        driver.implicitly_wait(delay)
        driver.get(
            "https://iforgot.apple.com/password/verify/appleid?language=en")
        # 暂停1s，否则找文本框输入文字会出错
        random_delay()
        # 等待文本框出现且可用，这里是根据class查找，文本框的id是变化的
        id_input = find_enable_by_class(driver, "iforgot-apple-id")
        id_input.click()
        id_input.send_keys(apple_id)
        # 识别验证码
        msg = ''
        count = 1
        while True:
            # 查找验证码图片，页面只有这一个图片，可根据标签定位
            img = find_element_by_css(
                driver, "img").get_attribute("src")
            img = img.replace('data:image/jpeg;base64, ', '')
            code = ocr.classification(img)
            # 查找验证码输入框，输入验证码
            code_input = find_element_by_class(driver, 'captcha-input')
            code_input.send_keys(code)
            # 等待按钮可点击(验证码输入框输入文字后该按钮从不可点击变成可点击）
            find_enable_by_class(driver, "last").click()
            try:
                # 正常情况
                msg = find_element_by_class(
                    driver, "subtitle").get_attribute("innerHTML")
                break
            except Exception as e:
                # 超时未跳转/解锁，顶部会有大字提示错误信息
                msg = find_element_by_class(
                    driver, "app-title").get_attribute("innerHTML")
                # 验证未通过(错误)
                if msg == "Reset your password":
                    # 红色错误提示信息
                    msg = find_element_by_class(
                        driver, "form-message").get_attribute("innerHTML").strip()
                    if msg == 'Your request could not be completed because of an error. Try again later.':
                        msg = msg + '(建议换个不同运营商的VPS尝试)'
                    if msg == "Please enter the characters you see or hear to continue.":
                        if count < 5:
                            print("验证码识别错误 (%s)，已尝试 %d 次，准备再试试..." %
                                  (apple_id, count))
                            count = count + 1
                            random_delay()
                            continue
                        else:
                            msg = "验证码识别错误 (%s)，已尝试 %d 次，再见！" % (
                                apple_id, count)
                            print(msg)
                    send_msg("出错啦！" + msg)
                    account["status"] = 0
                break

        print(msg)
        # 重置密码
        if msg == "Select what information you want to reset.":
            print("重置密码...")
            random_delay()
            find_enable_by_class(driver, "right-nav").click()

            message = find_element_by_class(
                driver, "description").get_attribute("innerHTML")
            if "Choose from the options below to reset the password" in message:
                # 找到回答安全问题前面的选择框点击
                find_element_by_class_all(
                    driver, "form-radiobutton-indicator")[1].click()
                find_enable_by_class(driver, "last").click()

            # 输入生日
            find_enable_by_class(driver, "date-input").send_keys(birthday)
            find_enable_by_class(driver, "last").click()

            # 回答问题 等待两个文本框出现
            questions = find_element_by_class_all(driver, "question")
            answers = find_element_by_class_all(driver, "security-answer")
            for index, q in enumerate(questions):
                answers[index].send_keys(question_dic[q.get_attribute("innerText")])
                random_delay()
            find_enable_by_class(driver, "last").click()
            # 设置密码 createPwd
            find_element_by_id(driver, "password")
            pwd_inputs = find_element_by_class_all(
                driver, "form-textbox-input")
            for index, input in enumerate(pwd_inputs):
                input.send_keys(new_generated_pwd)
                random_delay()
            find_enable_by_class(driver, "last").click()

            msg = apple_id + " 密码已重置为 %s" % new_generated_pwd
            account["passwd"] = new_generated_pwd
            account["last_reset_time"] = round(time.time())
            update_pwd(api_host, apple_id, new_generated_pwd)
            send_msg(msg)
            return True
        # 解锁
        elif msg == "Select how you want to unlock your account:":
            print("正在解锁 %s ..." % apple_id)
            find_element_by_id(driver, 'optionemail')
            oq = find_element_by_class_all(
                driver, "form-radiobutton-indicator")
            # 只能通过邮件解锁的情况
            if len(oq) == 1:
                oq[0].click()
                find_enable_by_class(driver, "last").click()
                msg = "大事不好了，%s 只能用邮件解锁，已经给你发邮件了..." % apple_id
                send_msg(msg)
                return
            # 通过回答问题解锁
            oq[1].click()
            find_enable_by_class(driver, "last").click()
            find_enable_by_class(driver, "date-input").send_keys(birthday)
            find_enable_by_class(driver, "last").click()
            # 回答问题 等待两个文本框出现
            questions = find_element_by_class_all(driver, "question")
            answers = find_element_by_class_all(driver, "security-answer")
            for i, q in enumerate(questions):
                answers[i].send_keys(question_dic[q.get_attribute("innerText")])
                random_delay()
            find_enable_by_class(driver, "last").click()
            # 解锁并修改密码
            find_element_by_class(driver, "pwdChange").click()
            # 等待密码框出现
            find_element_by_id(driver, "password")
            pwd_inputs = find_element_by_class_all(
                driver, "form-textbox-input")
            for i, input in enumerate(pwd_inputs):
                input.send_keys(new_generated_pwd)
                random_delay()
            find_enable_by_class(driver, "last").click()
            find_element_by_class(driver, "done")

            msg = apple_id + " 账号状态已解锁，新密码 %s " % new_generated_pwd
            account["passwd"] = new_generated_pwd
            account["last_reset_time"] = now
            update_pwd(api_host, apple_id, new_generated_pwd)
            send_msg(msg)
            return True
        # 双重认证
        elif msg == "Confirm your phone number.":
            print("正在关闭 %s 的双重认证..." % apple_id)
            find_element_by_class(driver, "button-caption-link").click()
            find_element_by_class_all(driver, "last")[1].click()
            # driver.find_element(by=By.XPATH, value="/html/body/div[5]/div/div/recovery-unenroll-start/div/idms-step/div/div/div/div[3]/idms-toolbar/div/div/div/button[1]").click()
            # 输入生日
            find_element_by_class(driver, "date-input").send_keys(birthday)
            find_enable_by_class(driver, "last").click()
            # 回答问题
            questions = find_element_by_class_all(driver, "question")
            answers = find_element_by_class_all(driver, "form-textbox-input")
            for i, q in enumerate(questions):
                answers[i].send_keys(question_dic[q.get_attribute("innerText")])
                random_delay()
            find_enable_by_class(driver, "last").click()
            random_delay()
            find_enable_by_class(driver, "last").click()
            random_delay()
            # 设置密码 createPwd
            pwd_inputs = find_element_by_class_all(
                driver, "form-textbox-input")
            for i, input in enumerate(pwd_inputs):
                input.send_keys(new_generated_pwd)
                random_delay()
            find_enable_by_class(driver, "last").click()
            find_element_by_class(driver, "idms-modal-content")
            random_delay()
            find_element_by_class_all(driver, "last")[1].click()

            msg = apple_id + " 双重认证已关闭，新密码 %s " % new_generated_pwd
            account["passwd"] = new_generated_pwd
            account["last_reset_time"] = now
            update_pwd(api_host, apple_id, new_generated_pwd)
            send_msg(msg)
            return True
        else:
            print("我无法理解")
    except Exception as e:
        msg = '检查 %s 时，超时了。%s' % (apple_id, str(e))
        account["status"] = 0
        send_msg(msg)
        create_error_file(driver.page_source)
    finally:
        print('退出浏览器')
        time.sleep(2)
        driver.quit()


apple_data = []
config = {}
save = False
with open(mypath + '/config.json', 'r', encoding='utf-8') as f:
    config = json.load(f)

new_generated_pwd = create_specify_pwd(config['generated_pwd'])
apple_ids = config['apple_id']
for index, value in enumerate(apple_ids):
    if index > 0:
        print('暂停10秒再处理下一条\n')
        time.sleep(10)

    count = 1
    flag = False
    while True:
        print('正在第%d次检查 %s' % (count, value["id"]))
        flag = check_appleid(value)
        if (flag):
            break
        else:
            count = count + 1
            if (count >= 3):
                break
    if flag:  # 只要有一个密码被修改，就重新保存
        save = flag
    # str_time = time.strftime(u"%Y年%m月%d日 %H:%M:%S",time.localtime(account['last_reset_time']))
    str_time = time.strftime("%Y年%m月%d日 %H:%M:%S",
                             time.localtime(round(time.time())))
    apple_data.append({"id": value['id'], "passwd": value['passwd'], "app_name": value['app_name'],
                       "status": value["status"], "last_reset_time": str_time})

# 构造一个 json 数据，供静态 HTML 使用
print('正在写入 JSON 数据...')
with open(mypath + '/id.json', 'w', encoding='utf-8') as f:
    json.dump(apple_data, f, ensure_ascii=False, indent=4)
if save:
    print('正在更新 config 数据...')
    config['generated_pwd'] = new_generated_pwd
    with open(mypath + '/config.json', 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=4)

print("执行完成！即将退出...")
time.sleep(3)
