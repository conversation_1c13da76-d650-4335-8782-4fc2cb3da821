import pandas as pd
import os

# Load the Excel file
excel_path = '/Users/<USER>/Desktop/接口使用统计.xlsx'  # 请将此路径替换为你的Excel文件路径
df = pd.read_excel(excel_path, usecols=[0], header=None, names=['API'])

# Directory to search in
search_directory = '/Users/<USER>/Documents/lazhuxiangmu/djc接入UP/QCYZT/QCYZT'

# Function to check if an API is used in a file
def is_api_used(api, file_path):
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
            return api in content
    except Exception as e:
        return False

# Check each API in the .h and .m files
results = []
used_api_indices = []
for idx, api in enumerate(df['API']):
    api_found = False
    for root, dirs, files in os.walk(search_directory):
        for file in files:
            if file.endswith('.h') or file.endswith('.m'):
                file_path = os.path.join(root, file)
                if is_api_used(api, file_path):
                    api_found = True
                    used_api_indices.append(idx + 1)  # Store the 1-based index of the API
                    break
        if api_found:
            break
    results.append(api_found)

# Print all indices of used APIs
print("可用的接口序号：", used_api_indices)

# Add results to DataFrame and save to a new Excel file
df['Is Used'] = results
output_path = '/Users/<USER>/Desktop/接口使用统计结果2.xlsx'  # 生成的结果文件路径，请替换为你希望保存的位置
df.to_excel(output_path, index=False)

print(f"结果已保存到 {output_path}")