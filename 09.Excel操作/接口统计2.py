import pandas as pd
import os

# 加载 Excel 文件
excel_path = '/Users/<USER>/Desktop/旧项目相关接口.xlsx'  # 上传的文件路径
search_directory = '/Users/<USER>/Documents/lazhuxiangmu/djc接入UP/QCYZT/QCYZT'  # 搜索目录路径，请替换为实际路径

# 读取 Excel 文件
df = pd.read_excel(excel_path)

# 创建新列用于存储搜索结果
df['A_Found'] = 0  # 对应请求地址（列 A）
df['B_Found'] = 0  # 对应请求地址新（列 B）


# 定义函数，用于检查 API 是否在文件中被使用
def is_api_used(api, file_path):
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
            return api in content
    except Exception:
        return False


# 遍历 Excel 数据中的每一行，检查每个 API 是否存在
for idx, row in df.iterrows():
    api_a = row['请求地址']
    api_b = row['请求地址新']

    # 初始化标志位
    found_a = False
    found_b = False

    # 遍历搜索目录
    for root, dirs, files in os.walk(search_directory):
        for file in files:
            if file.endswith('.h') or file.endswith('.m'):  # 只检查 .h 和 .m 文件
                file_path = os.path.join(root, file)
                # 检查请求地址 (列 A)
                if not found_a and is_api_used(api_a, file_path):
                    found_a = True
                # 检查请求地址新 (列 B)
                if not found_b and is_api_used(api_b, file_path):
                    found_b = True
                # 如果两个都找到了，提前退出
                if found_a and found_b:
                    break
        if found_a and found_b:
            break

    # 更新结果到 DataFrame
    df.at[idx, 'A_Found'] = 1 if found_a else 0
    df.at[idx, 'B_Found'] = 1 if found_b else 0

# 保存结果到新的 Excel 文件
output_path = '/Users/<USER>/Desktop/接口搜索结果.xlsx'  # 输出文件路径
df.to_excel(output_path, index=False)

print(f"结果已保存到 {output_path}")
