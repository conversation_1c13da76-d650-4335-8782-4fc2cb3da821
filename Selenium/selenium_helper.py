# selenium_helper.py
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

class SeleniumHelper:
    def __init__(self, driver, delay=10):
        self.driver = driver
        self.delay = delay

    # 查找元素是否存在（按类名）
    def wait_for_element_by_class(self, class_name):
        return WebDriverWait(self.driver, self.delay).until(
            EC.presence_of_element_located((By.CLASS_NAME, class_name)),
            f'未找到 Class 为 "{class_name}" 的元素'
        )

    # 查找所有符合条件的元素是否存在（按类名）
    def wait_for_all_elements_by_class(self, class_name):
        return WebDriverWait(self.driver, self.delay).until(
            EC.presence_of_all_elements_located((By.CLASS_NAME, class_name)),
            f'未找到 Class 为 "{class_name}" 的元素'
        )

    # 查找元素是否存在（按ID）
    def wait_for_element_by_id(self, element_id):
        return WebDriverWait(self.driver, self.delay).until(
            EC.presence_of_element_located((By.ID, element_id)),
            f'未找到 ID 为 "{element_id}" 的元素'
        )

    # 查找元素并且是可用状态（按类名）
    def wait_for_clickable_element_by_class(self, class_name):
        return WebDriverWait(self.driver, self.delay).until(
            EC.element_to_be_clickable((By.CLASS_NAME, class_name)),
            f'元素 "{class_name}" 不可用'
        )

    # 查找元素并且是可用状态（按CSS选择器）
    def wait_for_clickable_element_by_css(self, css_selector):
        return WebDriverWait(self.driver, self.delay).until(
            EC.element_to_be_clickable((By.CSS_SELECTOR, css_selector)),
            f'CSS 元素 "{css_selector}" 不可用'
        )

    # 查找元素并且是可用状态（按ID）
    def wait_for_clickable_element_by_id(self, element_id):
        return WebDriverWait(self.driver, self.delay).until(
            EC.element_to_be_clickable((By.ID, element_id)),
            f'ID 元素 "{element_id}" 不可用'
        )