import os
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys  # 导入Keys类

# 获取当前脚本所在路径
mypath = os.path.split(os.path.realpath(__file__))[0]
chromedriver = ""

# 根据操作系统设置chromedriver路径
if os.name == "nt":
    chromedriver = mypath + "\\chromedriver.exe"  # Windows路径
elif os.name == "posix":
    chromedriver = '/usr/local/bin/chromedriver'  # macOS或Linux路径

# 设置ChromeDriver服务
service = Service(chromedriver)
os.environ["webdriver.chrome.driver"] = chromedriver

# 初始化Chrome浏览器
driver = webdriver.Chrome(service=service)

try:
    # 打开百度贴吧主页
    driver.get("https://tieba.baidu.com/")

    # 使用ID定位搜索框并输入关键字
    search_box = WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.ID, "wd1"))
    )
    search_box.send_keys("Python")
    search_box.send_keys(Keys.RETURN)

    # 等待搜索结果加载并获取标题
    results = WebDriverWait(driver, 10).until(
        EC.presence_of_all_elements_located((By.CSS_SELECTOR, "a.j_th_tit"))
    )

    # 打印每个结果的标题
    for result in results:
        print(result.text)

finally:
    # 关闭浏览器
    driver.quit()