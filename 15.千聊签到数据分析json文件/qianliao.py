# import json
# import re
# from openpyxl import Workbook
#
# # 读取JSON文件
# with open('xianyu.json', 'r', encoding='utf-8') as f:
#     data = json.load(f)
#
# # 获取所有content字段下的内容
# contents = [d['content'] for d in data['data']['affairList']]
#
# # 定义正则表达式来匹配链接和标题
# link_pattern = r'https?://[^\s<>"]+|www\.[^\s<>"]+'
# title_pattern = r'【(?!闲鱼)(.*?)】'
#
# # 创建Excel文件
# wb = Workbook()
# ws = wb.active
#
# # 添加表头
# ws.append(['链接', '标题'])
#
# # 遍历所有content字段下的内容，提取链接和标题
# for content in contents:
#     # 匹配链接和标题
#     links = re.findall(link_pattern, content)
#     titles = re.findall(title_pattern, content)
#
#     # 将链接和标题放到字典中，加入到字典数组中
#     link_title_dict_list = [{'link': link, 'title': title} for link, title in zip(links, titles)]
#
#     # 将字典数组中的内容写入到Excel中
#     for d in link_title_dict_list:
#         ws.append([d['link'], d['title']])
#
# # 保存Excel文件
# wb.save('xianyu_links_titles.xlsx')

import requests
from bs4 import BeautifulSoup

url = 'https://h5.m.goofish.com/item?id=711843075486&ut_sk=1.WyOnTYx0QnYDAHhpwl8FaVOe_21407387_1680822036705.copy.detail.711843075486.2214546426076&forceFlush=1&ownerId=679138070be56c526b336dcb22d76a83&un=a9c459bce942ce648c235858d8a68be5&share_crt_v=1&un_site=77&spm=a2159r.13376460.0.0&sp_abtk=common_xianyu_commonInfo&sp_tk=WDk4cWRrRVZUazA%3D&cpp=1&shareurl=true&short_name=h.UHO54dI&bxsign=scdDNmLxZtUfkRye19TO1EID3z526eei0JskHTaEsOir0MJKpPq5OzrbV_nndWqZkh1rqQlNKpvqkq4R7nlbUFsZi6kEWDMFiY5kn11QFBuYsGZDklLY-gEckJBeY552IpCmODizluw-NYmoW-Yx0ocnQ'
response = requests.get(url)
soup = BeautifulSoup(response.text, 'html.parser')
print(soup.prettify())
