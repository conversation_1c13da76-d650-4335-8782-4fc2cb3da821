# print('hello, world!')

# # 数组
# print([1, '12', 3, 4, 5])

# # 元祖
# print((1, 2, 3))


# # 集合
# print(set([1, 1, 2]))

# # 字典
# print({'name' : 'zenglun', 'age' : 20})


# a = 1 + 1
# print(a)


# for i in range(1, 6):
# 	print(i)

# _list = [1, 2, 34, 3]
# _list.append('test')
# _list.insert(0, 'abc')
# _list += ['bcd']
# print(_list)
# del _list[2]
# print(_list)
# _list.remove('test')
# print(_list)
# print(_list.pop(0))

# _list.sort()
# print(_list)

# for num in _list:
# 	print(num)

# _dict = dict(name = 'zenglun', age = 23)
# _dict['age']  = 32
# _dict['height'] = 174
# for key, value in _dict.items():
# 	print(key, value)
# for key in _dict.keys():
# 	print(key)


# _set = set([1, 2, '3', 4])
# _set.add('test')
# _set.remove('test')
# _set2 = set([2, 4, 'ab'])
# print(_set | _set2)
# print(_set & _set2)


# from queue import Queue
# _q = Queue()
# _q.put('1')
# _q.put(2)
# _q.put(3)
# # print(_q.qsize())
# while not _q.empty():
# 	print(_q.get())



# import re
# _str = '今天，天气, 天气不错啊'
# _str = _str.replace('天气', '阳光')
# print(_str)
# _str = re.sub('阳光', '天气', _str, 1)
# print(_str)
# print(_str.split(','))
# for s in _str:
# 	print(s)

# print(_str.find('天'))

# import os
# print(os.getcwd())
# print(os.path.exists('/Users/<USER>/Desktop/Python'))

# import re
# print(re.findall('[0-9]+', '今天天气123不错啊456'))
# print(re.sub('[0-9]+', 'haha', '今天天气123不错啊456'))

# print(re.findall('\d', '今天天气123不错啊456'))
# print(re.findall('\d+', '今天天气123不错啊456'))
# print(re.findall('\w', '今天天气123abc_不错啊456'))
# print(re.findall('\w+', '今天天气123abc_不错啊456'))
# print(re.findall('\s', '今天天气123abc_不  错啊456'))
# print(re.findall('\S', '今天天气123abc_不  错啊456'))

# import time
# print(time.time())
# print(time.localtime())
# print(time.strftime('%Y-%m-%d %H:%M:%S', time.localtime()))

# time1 = time.strptime('2000-01-01 00:00:00', '%Y-%m-%d %H:%M:%S') 
# time2 = int(time.mktime(time1))
# print(time2) # 10时间戳

# import datetime
# str(datetime.date.today())
# print(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
#
#
# import json
# _str = json.dumps({'test' : 1, 'age' : '2'})
# _dict = json.loads(_str)
# print(_dict)
#
# str = '天气'
# print('\nhaha%s' % str)

# def check_appleid(value):
#     print(value)
#     return value == 2
#     # return False
#
# count = 1
# flag = False
# while True:
#     flag = check_appleid(count)
#     if (flag):
#         break
#     else:
#         count = count + 1
#         if (count >= 3):
#             break
# print(count, flag)

from multiprocessing import Process
import os

def run_pro(name):
    print('子进程%s--%s正在运行'%(name,os.getpid()))

if __name__ == '__main__':
    print('主进程%s正在运行'%(os.getpid()))
    for i in range(1,6):
        p = Process(target=run_pro,args=(str(i),))
        print('进程将要开始运行')
        p.start()
    p.join()
    print('进程结束')
