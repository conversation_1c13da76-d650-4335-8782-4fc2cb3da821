#学习正则，过滤掉无效词
import re
content1 = '2019-12-15 12:00'
content2 = '2019-12-17 12:55'
content3 = '2019-12-22 13:21'
pattern = re.compile('\d{2}:\d{2}')
result1 = re.sub(pattern, '', content1)
result2 = re.sub(pattern, '', content2)
result3 = re.sub(pattern, '', content3)
print(result1, result2, result3)


# with open ('data.txt', 'r', encoding='utf-8') as file :
# 	text_list = file.read().split('\n')
#
# # print(len(text_list))
#
# for text in text_list:
# 	if len(text) < 5 or len(text) > 8:
# 		continue
# 	# if '赚' in text or '挣' in text :
# 	# 	continue
# 	if len(re.findall('|'.join(['挣', '赚', '\d', '[a-zA-Z]']), text)) > 0 :
# 	# 如果text包含“挣、赚、数字、字母”
# 		continue
# 	if '伪原创' in text and '文章' in text :
# 		continue
# 	if text.isdigit():  #纯数字
# 		continue
#
#
# 	# is_true = False
# 	# for i in ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9']:
# 	# 	if i in text:
# 	# 		is_true = True
# 	# 		break
# 	# if is_true :
# 	# 	continue
#
# 	# if len(re.findall('\d', text)) > 0 :
# 	# 	continue
#
# 	if ' ' in text :
# 		text = text.replace(' ', '')
#
# 	print(text)
