 ### 数据结构操作

</br>

#### 列表 list

```python
# 定义
_list = list() # 定义一个空列表
_list = [] # 定义一个空列表
_list = [1] # 定义一个有数据的列表

# 增加元素
_list.append('test') # 插在最后
_list.insert(0,'test') # 第一个参数为插入位置
_list += ['test'] # 合并另一个列表元素

# 删除元素
del _list[0] # 0为具体下标
_list.remove('test') # 删除第一个匹配的元素
_list.pop(0) # 0为具体下标

# 访问元素
_list[0] # 取下标为0或第一个元素
_list[-1] # 取倒数第一个元素
_list[1:] # 从下标为1或第2个元素开始截取到最后一个
_list[:10] # 从下标为0或第1个元素截取到下标为9或第10个元素
_list[5:20] # 从下标为5或第6个元素截取到第20个元素

# 元素合并
''.join(_list) # 引号里放入连接符合

# 元素排序
_list.sort()

# 循环遍历
for l in _list:
    print(l)
```

</br>

#### 字典 dict

```python
# 定义
_dict = dict() # 定义一个空字典
_dict = {} # 定义一个空字典
_dict = {'name':'xiaozeng'} # 定义一个有数据的字典

# 增加\修改元素
_dict['key'] = 'value'

# 访问元素
_dict['key']

# 删除元素
del _dict['key'] # key代表指定的键

# 循环遍历
for key,value in _dict.items(): # 键值一起遍历
    print(key,value)

for key in _dict.keys(): # 单独遍历键
    print(key)

for value in _dict.values(): # 单独遍历值
    print(value)
```

</br>

#### 集合 set

```python
# 定义
_set = set() # 定义一个空集合
_set = set([1]) # 定义一个有数据的集合

# 增加元素
_set.add('test')

# 删除元素
_set.remove('test')

# 并集
_set1 | _set2
# 交集
_set1 & _set2
# 差集
_set1 - _set2

# 循环遍历
for s in _set:
    print(s)
```

</br>

#### 队列 queue

```python
# 导入
from queue import Queue

# 定义
_q = Queue()

# 增加元素
_q.put('test')

# 取值
_q.get() # 取最先加入的值

# 打印元素个数
_q.qsize()

# 循环遍历
while not _q.empty(): # _q.empty() 队列为空则返回True
    print(_q.get())
```

</br>

### 常用命令大全

</br>

```python
str(1) # 整型转文本
int('1') # 文本转整型
print('!') # 打印信息
input(':') # 前端暂停输入信息
type('test') # 取目标类型
len('test') # 字符串长度，数据结构元素个数

# 文本替换
_str = _str.replace('替换目标','替换成') 
_str = re.sub('替换目标','替换成',_str)

# 文本分割
_str.split('') # 引号内填写具体分割符号

# 文本逐字分割
for s in _str:
    print(s)
    
# 是否为纯数字
_str.isdigit() # 纯数字返回True
# 是否为纯英文
_str.encode('utf-8').isalpha() # 先将汉字转化后再判断

# 寻找文本
_str.find('test') # 在_str里找test
re.findall('test',_str) # 在_str里找test

# 字母大小写转换
_str.upper() # 全部转换为大写
_str.lower() # 全部转换为小写

# url编码
from urllib.parse import quote
test = quote('你好','utf-8') # url编码

from urllib.parse import unquote
test = unquote('%E4%BD%A0%E5%A5%BD','utf-8') # url解码

# 字典排序
_dict = {1:4,8:5,3:6,2:7}
# True表示降序，0表示对键排序，1表示对值排序，结果以列表返回
_dict = sorted(_dict.items(),key=lambda x:x[0],reverse=True)

# 定义一个默认类型的字典
from collections import defaultdict
```

</br>

### 常用内置模块

</br>

#### os 系统操作
```python
# 导入
import os

os.getcwd() # 当前程序路径
os.path.exists('e:\\test') # 判断路径是否存在
os.mkdir('e:\\test') # 新建文件夹
os.listdir('e:\\test') # 目标路径里的所有文件夹和文件
os.walk('e:\\test') # 目标路径里的所有文件夹和文件
os.sep # 路径连接符 自动根据当前操作系统转换 win: 'test\\test' mac 'test/test' 可以通用写成 'test' +os.sep+ 'test'
```

</br>

#### re 正则表达式
```python
# 导入
import re

# 寻找文本
re.findall('匹配规则','目标文本')
re.findall('匹配规则','目标文本',flags=re.I) # 忽略大小写
re.findall('匹配规则','目标文本',flags=re.S) # 匹配包含换行
re.findall('匹配规则','目标文本',flags=re.I|re.S) # 忽略大小写 且 匹配包含换行

# 寻找文本后批量替换
re.sub('匹配规则','替换成','目标文本')
re.sub('匹配规则','替换成','目标文本',flags=re.I) # 忽略大小写
re.sub('匹配规则','替换成','目标文本',flags=re.S) # 匹配包含换行
re.sub('匹配规则','替换成','目标文本',flags=re.I|re.S) # 忽略大小写 且 匹配包含换行

# re常见匹配规则
'\d' # 匹配数字
'\d+' # 尽可能匹配多个
'\w' # 匹配数字字母下划线
'\w+' # 尽可能匹配多个
'\W' # 匹配非数字字母下划线
'\W+' # 尽可能匹配多个
'\s' # 匹配任意空白字符
'\s+' # 尽可能匹配多个
'\S' # 匹配任意非空字符
'\S+' # 尽可能匹配多个
'a(.*?)b' # 匹配a和b之间的内容
'a|b' # 即匹配a也匹配b，只要有

# 更多匹配规则
https://www.runoob.com/python3/python3-reg-expressions.html
```

</br>

#### time 时间操作
```python
# 导入
import time

time.time() # 当前时间对应的时间戳，秒为单位
time.time() - time.time() # 可以计算两个时间段相差秒数
time.sleep(1) # 程序延迟一秒后继续
time.localtime(time.time()) # 当前时间
time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()) # 当前时间格式化 2000-01-01 00:00:00

# 时间转为10时间戳
time1 = time.strptime('2000-01-01 00:00:00', '%Y-%m-%d %H:%M:%S')
time2 = int(time.mktime(time1))
print(time2) # 10时间戳

# 10时间戳转为时间
time1 = time.localtime(946656000)
time2 = time.strftime("%Y-%m-%d %H:%M:%S", time1)
print(time2)
```

</br>

#### datetime 日期操作
```python
# 导入
import datetime

str(datetime.date.today()) # 当前日期 2000-01-01
str(datetime.date.today() - datetime.timedelta(days=1)) # 昨日日期 2000-01-01
datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S') # 当前时间格式化 2000-01-01 00:00:00
```

</br>

#### random 随机数
```python
# 导入
import random

random.random() # 生成随机数
random.randint(0,9) # 生成 0 ~ 9 之间的随机整数
random.sample(range(0,9),5) # 在 0 ~ 9 之间随机生成5个整数
```

</br>

#### csv
```python
# 导入
import csv

# 读取csv数据
with open('test.csv',encoding='utf-8') as file:
    data = csv.reader(file)
    # 打印每一行
    for row in data:
        print(row)
```

</br>

#### pickle 泡菜存储
```python
# 导入
import pickle

# 读取数据
with open('test.pk','rb') as file:
    _dict = pickle.load(file)
# 存储数据
_dict = dict()
with open('test.pk','wb') as file:
    pickle.dump(_dict,file)
```

</br>

#### json 文本字典互转
```python
# 导入
import json

_str = json.dumps({'1':'2'}) # 字典转文本
_dict = json.loads(_str) # 文本转字典
```

</br>

### 常用第三方模块

</br>

#### requests 爬虫 网络访问
```python
# 导入
import requests

ssion = requests.Session() # 创建一个保持cookie的会话身份
ssion.headers = {'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.96 Safari/537.36'} # 伪造请求头
rel = ssion.get('http://www.baidu.com/') # 访问一个链接
rel = ssion.post('http://www.baidu.com/',data={'user':'xiaozeng','password':'123456'}) # 提交一个表单

rel.content # 源代码
rel.status_code # 状态码

# 中文文档
https://docs.python-requests.org/zh_CN/latest/user/quickstart.html
```

</br>

#### splinter 模拟浏览器
```python
# 导入
from splinter.browser import Browser

browser = Browser(driver_name='chrome') # 初始化浏览器
browser.visit('https://www.zhihu.com/') # 访问指定页面
browser.html # 取源代码
browser.title # 取网页标题

# 将返回为json格式的网页源代码里的pre标签去除并转为字典格式
rel_obj = json.loads(re.findall('<pre style="word-wrap: break-word; white-space: pre-wrap;">(.*?)</pre>',browser.html,re.S)[0])

browser.find_by_id('test')[0].fill('test') # 通过元素id寻找目标组件并写入
browser.find_by_css('test')[0].fill('test') # 通过元素css寻找目标组件并写入
browser.find_by_text('test')[0].click() # 通过元素标题寻找目标组件并点击
browser.find_by_value('test')[0].click() # 通过元素值寻找目标组件并点击

# 官方文档
https://splinter.readthedocs.io/en/latest/
```

</br>

#### jieba 分词
```python
# 导入
import jieba

jieba.load_userdict("dict.txt") # 导入自定义词典 txt 一行一个：词语 n
jieba.del_word('test') # 删除指定词 删除后不识别

data = jieba.cut('test',cut_all=True)
print("/".join(data)) #全模式

data = jieba.cut('test',cut_all=False)
print("/".join(data)) #精确模式 默认是

data = jieba.cut_for_search('test') # 搜索引擎模式
print("/".join(data))

import jieba.posseg
data = jieba.posseg.cut('test') # 带词性
print([(word,flag) for word,flag in data])
```

</br>

#### xlrd xls文件读取
```python
# 导入
import xlrd

xls = xlrd.open_workbook('test.xls') # 读取文件
sheet1 = xls.sheets()[0] # 通过索引顺序获取sheet
sheet2 = xls.sheet_by_index(1) # 通过索引顺序获取sheet
sheet3 = xls.sheet_by_name('test') # 通过名称获取sheet
sheetall = xls.sheet_names() # 通过名称获取所有sheet页的内存对象

sheet1.row_values(0) # 获取整行的值
sheet1.col_values(0) # 获取整列的值

# 循环行列表数据
nrows = sheet1.nrows
for r in range(nrows):
    print(sheet1.row_values(r))

# 循环行列表数据
ncols = sheet1.ncols
for c in range(ncols):
    print(sheet1.row_values(c))

# 使用单元格坐标取值
cell_A1 = sheet1.cell(0,0).value
cell_C4 = sheet1.cell(2,3).value
# 使用行列索引取值
cell_A1 = sheet1.row(0)[0].value
cell_A2 = sheet1.col(1)[0].value
```

</br>

#### xlwt xls文件写入
```python
# 导入
import xlwt

xls = xlwt.Workbook(encoding='utf-8') # 创建一个xls工作簿
# 创建一个sheet对象,第二个参数是指单元格是否允许重设置，默认为False
sheet = xls.add_sheet('test', cell_overwrite_ok=False)
sheet.write(0,0,'test') # 写入到指定单元格 第一个参数代表行，第二个参数是列，第三个参数是内容

style = xlwt.XFStyle() # 初始化样式
font = xlwt.Font() # 为样式创建字体
font.name = 'Times New Roman' # 选择字体
font.bold = True # 黑体
style.font = font # 设定样式
sheet.write(0,1,'test',style) # 加入样式 写入到指定单元格

xls.save('test.xls') # 保存文件
```

</br>

#### openpyxl xlsx文件读取和写入
```python
# 导入
import openpyxl

# 读取
xlsx = openpyxl.load_workbook('test.xlsx') # 选择Excel表
sheet = xlsx['Sheet1'] # 通过名称选择表单
sheet.cell(1,1).value # 获取第一个单元格的值,坐标从1开始

# 写入
xlsx = openpyxl.Workbook() # 创建Excel表
sheet = xlsx.create_sheet('Sheet1',index=0) # 创建表单
sheet.append([1 ,2, 3, 4, 5]) # 写入一行
sheet['A2'] = 6 # 单个写入

xlsx.save('test.xlsx') # 保存写入的数据
```

</br>

#### pillow 图片操作
```python
# 安装
pip3 install pillow
# 导入
from PIL import Image

img1 = Image.open('test1.png') # 打开一张图片
width,height = img1.size # 获取宽高
img1 = img1.resize((width,height)) # 修改图片宽高

img2 = Image.open('test2.png')
img1.paste(img2,box=(width,height)) # 图片叠加合成 box为图2在图1的坐标位置

img1.save('test1.png','png') # 保存修改
```

</br>

#### yagmail 邮件操作
```python
# 导入
import yagmail

# 链接服务器
user = '<EMAIL>'
password = '123456789'
host = 'smtp.qq.com'
mail = yagmail.SMTP(user=user,password=password,host=host,encoding='gbk')

# 发送邮件
to = ['<EMAIL>']
subject = '邮件标题'
contents = '邮件正文'
mail.send(to=to,subject=subject,contents=contents)

# 加入附件并发送
attachments = ['test.txt']
mail.send(to=to,subject=subject,contents=contents,attachments=attachments)
```

</br>

#### lxml 解析html
```python
html=etree.HTML(text) #初始化一个XPath解析对象

html.xpath('//a/text()') # 获取a标签的标题
html.xpath('//a/@herf') # 获取a标签的链接

# 中文文档
https://www.w3cschool.cn/lxml/_lxml-ziay3fjr.html
```
</br>

#### cv2 moviepy 视频处理
```python
# 安装
pip3 install opencv-python
pip3 install moviepy

# 导入
import cv2
from moviepy.editor import *

# cv2合成图片
path = 'test.mp4' # 存储路径
fourcc = cv2.VideoWriter_fourcc('m','p','4','v') # 编码格式
fps = 24 # 帧率，设置24，表示24帧为1秒，一张图片希望显示一秒，则需要反复插入24次
size = (828,1792) # 原图宽高，所有合成图片宽高必须一致
video = cv2.VideoWriter(path,fourcc,fps,size) # 定义一个视频对象

for i in range(1,2):
	img = cv2.imread('%s.png' % i) # 打开图片
	# 反复插入与帧率一样的张数
	for f in range(0,fps):
		video.write(img)

# 结束
video.release()

# ------------------------------------------------------------------------------

# moviepy合成视频
video1 = VideoFileClip('mp4' + os.sep + '1.mp4') # 打开视频1
video2 = VideoFileClip('mp4' + os.sep + '2.mp4') # 打开视频2

video_list = [video1,video2] # 视频统计加入一个列表
video = concatenate_videoclips(video_list) # 合成列表里的所有视频

video.write_videofile('mp4' + os.sep + '3.mp4') # 保存视频
```

</br>

```python
# 模块安装 使用国内镜像，速度快
pip3 install -i https://pypi.tuna.tsinghua.edu.cn/simple jieba

# chromedriver 下载地址
http://npm.taobao.org/mirrors/chromedriver/
```
