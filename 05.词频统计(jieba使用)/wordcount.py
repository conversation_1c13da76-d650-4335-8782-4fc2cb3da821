# 学习使用jieba来进行分词，词频统计
import jieba, jieba.posseg as jb

print(jb.cut('今天天气真好啊'))
for i in jb.cut('今天天气真好啊') :
	print(i.word, i.flag)

for w,f in jb.cut('今天天气不错') :
	print(w, f)


jieba.load_userdict('dict.txt')  # 添加自定义词典, 以便包含 jieba 词库里没有的词
jieba.del_word('媒体广告')  # 删除认为不是词组的词

with open('keyword.txt', 'r', encoding='utf-8') as file :
	keyword_list = file.read().split('\n')

word_count = dict()
for keyword in keyword_list:
	for word,flag in jb.cut(keyword):
		if word in word_count.keys():
			word_count[word] += 1
		else:
			word_count[word] = 1

for word,count in word_count.items():
	print('%s\t%d' % (word, count))


