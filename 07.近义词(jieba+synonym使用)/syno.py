#近义词替换，学习jieba、synonyms
import synonyms, jieba.posseg as jp

print("人脸: ", synonyms.nearby("人脸"))
print("识别: ", synonyms.nearby("识别"))

word_list, word_count = synonyms.nearby("喜欢")
print(word_list, '\n', word_count)

article = '赚钱是通过自身的资本或者劳动力产生收入的一个过程'

word_arr = list()
for word, flag in jp.cut(article):
	if len(word) < 2:
		word_arr.append(word)
	else:
		word_list = synonyms.nearby(word)[0]
		if len(word_list) > 1:
			word_arr.append(word_list[1])
		else:
			word_arr.append(word_list[0])

print(''.join(word_arr))

word_dict = dict()
for word, flag in jp.cut(article):
	if len(word) < 2:
		continue
	word_list = synonyms.nearby(word)[0]
	for syno_word in word_list:
		if word == syno_word:
			continue
		word_dict[word] = syno_word
		break

for word, syno_word in word_dict.items():
	article = article.replace(word, syno_word)

print(article) 