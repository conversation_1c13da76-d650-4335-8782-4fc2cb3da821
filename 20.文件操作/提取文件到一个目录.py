import os
import shutil


def find_and_copy_files(src_dir, dst_dir, extensions):
    for root, dirs, files in os.walk(src_dir):
        for file in files:
            if file.endswith(tuple(extensions)):
                src_file_path = os.path.join(root, file)
                dst_file_path = os.path.join(dst_dir, file)

                # 确保源文件和目标文件不是同一个文件
                if os.path.abspath(src_file_path) == os.path.abspath(dst_file_path):
                    print(f"Skipping same file: {src_file_path}")
                    continue

                # 确保目标目录存在
                os.makedirs(os.path.dirname(dst_file_path), exist_ok=True)

                # 如果目标文件已经存在，添加一个后缀
                counter = 1
                new_dst_file_path = dst_file_path
                while os.path.exists(new_dst_file_path):
                    base, ext = os.path.splitext(dst_file_path)
                    new_dst_file_path = f"{base}_{counter}{ext}"
                    counter += 1

                shutil.copy2(src_file_path, new_dst_file_path)
                print(f"Copied {src_file_path} to {new_dst_file_path}")


src_dir = "/Users/<USER>/Documents/lazhuxiangmu/优品云指标/Modules/CNCommonUI/CNCommonUI/Classes"
dst_dir = "/Users/<USER>/Documents/lazhuxiangmu/优品云指标/Modules/CNCommonUI/CNCommonUI/合并"
extensions = ['.m', '.h']

find_and_copy_files(src_dir, dst_dir, extensions)