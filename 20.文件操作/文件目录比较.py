import os
import filecmp
import difflib


def list_files(dir_path):
    file_list = []
    for root, dirs, files in os.walk(dir_path):
        for file in files:
            if not file.startswith('.'):  # 忽略以 ._ 开头的文件
                relative_path = os.path.relpath(os.path.join(root, file), dir_path)
                file_list.append(relative_path)
    return set(file_list)


def compare_file_contents(file1, file2):
    try:
        with open(file1, 'r', errors='ignore') as f1, open(file2, 'r', errors='ignore') as f2:
            f1_lines = f1.readlines()
            f2_lines = f2.readlines()
    except UnicodeDecodeError:
        with open(file1, 'r', encoding='latin1') as f1, open(file2, 'r', encoding='latin1') as f2:
            f1_lines = f1.readlines()
            f2_lines = f2.readlines()

    diff = difflib.unified_diff(f1_lines, f2_lines, fromfile=file1, tofile=file2)
    return list(diff)


def compare_directories(dir1, dir2, output_dir):
    files_in_dir1 = list_files(dir1)
    files_in_dir2 = list_files(dir2)

    common_files = files_in_dir1.intersection(files_in_dir2)
    differences = {}

    for file in common_files:
        file1 = os.path.join(dir1, file)
        file2 = os.path.join(dir2, file)
        if not filecmp.cmp(file1, file2, shallow=False):  # 如果文件内容不同
            diff = compare_file_contents(file1, file2)
            if diff:
                diff_file_path = os.path.join(output_dir, file.replace('/', '_') + '.diff')
                os.makedirs(os.path.dirname(diff_file_path), exist_ok=True)
                with open(diff_file_path, 'w') as diff_file:
                    diff_file.writelines(diff)
                differences[file] = diff_file_path

    return differences


# Paths to the extracted directories
dir1 = '/Users/<USER>/Documents/lazhuxiangmu/优品云指标/Modules/UPMarketUISDK/UPMarketUISDK/Classes'
dir2 = '/Users/<USER>/Documents/lazhuxiangmu/优品Demo/DJC 2/DJC-iOS-SDK/MarketUI-SDK-iOS/UPMarketUISDK/UPMarketUISDK'

# Path to the output directory for differences
output_dir = '/Users/<USER>/Documents/differences_output'

# Compare the directories and save the differences
differences = compare_directories(dir1, dir2, output_dir)

# Print the results
print("Differences have been written to the following files:")
for file, diff_path in differences.items():
    print(f"{file}: {diff_path}")