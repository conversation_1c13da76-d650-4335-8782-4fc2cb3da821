import os

def convert_to_md(file_path, output_dir):
    # 确保输出目录存在
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as file:
        content = file.read()

    # 创建对应的.md文件名
    base_name = os.path.basename(file_path)
    file_name, file_ext = os.path.splitext(base_name)

    # 根据文件类型添加后缀
    if file_ext == '.h':
        md_file_name = f"{file_name}_h.md"
    elif file_ext == '.m':
        md_file_name = f"{file_name}_m.md"
    else:
        print(f"Skipping unsupported file: {file_path}")
        return

    md_file_path = os.path.join(output_dir, md_file_name)

    # 将内容写入.md文件
    with open(md_file_path, 'w', encoding='utf-8') as md_file:
        md_file.write(f"```{file_ext[1:]}\n{content}\n```")

    print(f"Converted {file_path} to {md_file_path}")

def convert_directory_to_md(directory, output_dir):
    # 遍历目录中的所有文件
    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith('.h') or file.endswith('.m'):
                file_path = os.path.join(root, file)
                convert_to_md(file_path, output_dir)

if __name__ == "__main__":
    # 设置输入目录和输出目录
    input_directory = '/Users/<USER>/Desktop/知识库临时'  # 替换为你的.h和.m文件所在的目录
    output_directory = '/Users/<USER>/Desktop/知识库临时/md'  # 替换为你想保存.md文件的目录

    # 转换目录中的所有.h和.m文件
    convert_directory_to_md(input_directory, output_directory)