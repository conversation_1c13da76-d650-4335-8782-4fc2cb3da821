from pptx import Presentation
import os
from pathlib import Path


def ppt_to_md(ppt_path, output_dir):
    """
    将单个PPT文件转换为MD文件
    """
    try:
        prs = Presentation(ppt_path)
        md_content = []

        # 添加标题
        md_content.append(f"# {Path(ppt_path).stem}\n")

        for slide_number, slide in enumerate(prs.slides, 1):
            # 添加每页幻灯片的标题
            md_content.append(f"## Slide {slide_number}\n")

            for shape in slide.shapes:
                if hasattr(shape, "text") and shape.text.strip():
                    md_content.append(shape.text.strip())

            md_content.append("\n")  # 在每页幻灯片后添加空行

        # 生成输出文件路径
        output_file = os.path.join(output_dir, f"{Path(ppt_path).stem}.md")

        # 写入文件
        with open(output_file, "w", encoding="utf-8") as f:
            f.write("\n".join(md_content))

        return True
    except Exception as e:
        print(f"处理文件 {ppt_path} 时出错: {str(e)}")
        return False


def batch_convert_ppts(input_dir, output_dir):
    """
    批量转换目录下的所有PPT文件
    """
    # 创建输出目录（如果不存在）
    os.makedirs(output_dir, exist_ok=True)

    # 支持的文件扩展名
    ppt_extensions = ('.pptx', '.ppt')

    # 统计信息
    total_files = 0
    success_files = 0

    # 遍历目录
    for file in os.listdir(input_dir):
        if file.lower().endswith(ppt_extensions):
            total_files += 1
            ppt_path = os.path.join(input_dir, file)

            print(f"正在处理: {file}")
            if ppt_to_md(ppt_path, output_dir):
                success_files += 1

    # 打印统计信息
    print(f"\n转换完成！")
    print(f"总文件数: {total_files}")
    print(f"成功转换: {success_files}")
    print(f"失败数量: {total_files - success_files}")


# 使用示例
if __name__ == "__main__":
    input_dir = "/Users/<USER>/Desktop/导入/PPT/PPT"  # 输入PPT文件夹路径
    output_dir = "/Users/<USER>/Desktop/导入/PPT/PPT/MD"  # 输出MD文件夹路径

    batch_convert_ppts(input_dir, output_dir)